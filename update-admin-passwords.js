const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function updateAdminPasswords() {
    let connection;
    
    try {
        // 数据库连接配置
        const dbConfig = {
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: '', // 空密码
            database: 'smart_goose_saas_platform',
            charset: 'utf8mb4'
        };
        
        console.log('🔗 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 生成正确的密码哈希
        console.log('🔐 生成密码哈希...');
        const superAdminHash = await bcrypt.hash('admin123', 10);
        const adminHash = await bcrypt.hash('admin123', 10);
        
        // 更新超级管理员密码
        console.log('👤 更新超级管理员密码...');
        await connection.execute(`
            UPDATE platform_admins 
            SET password = ?, email = ?, updated_at = CURRENT_TIMESTAMP
            WHERE username = ?
        `, [superAdminHash, '<EMAIL>', 'super_admin']);
        
        // 更新普通管理员密码
        console.log('👤 更新普通管理员密码...');
        await connection.execute(`
            UPDATE platform_admins 
            SET password = ?, email = ?, updated_at = CURRENT_TIMESTAMP
            WHERE username = ?
        `, [adminHash, '<EMAIL>', 'admin']);
        
        // 验证更新结果
        console.log('🔍 验证更新结果...');
        const [users] = await connection.execute(
            'SELECT id, username, email, name, role, status FROM platform_admins ORDER BY id'
        );
        
        console.log('✅ 管理员用户列表:');
        users.forEach(user => {
            console.log(`   ID: ${user.id}, 用户名: ${user.username}, 邮箱: ${user.email}, 角色: ${user.role}`);
        });
        
        // 测试密码验证
        console.log('🔐 测试密码验证...');
        const [testUser] = await connection.execute(
            'SELECT password FROM platform_admins WHERE username = ?',
            ['super_admin']
        );
        
        if (testUser.length > 0) {
            const isValid = await bcrypt.compare('admin123', testUser[0].password);
            console.log('✅ 密码验证结果:', isValid ? '成功' : '失败');
        }
        
        console.log('\n🎉 密码更新完成！');
        console.log('📝 登录信息:');
        console.log('   URL: http://localhost:4001');
        console.log('   超级管理员 - 用户名: super_admin, 密码: admin123');
        console.log('   普通管理员 - 用户名: admin, 密码: admin123');
        
    } catch (error) {
        console.error('❌ 更新失败:', error.message);
        console.error('详细错误:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 运行更新脚本
updateAdminPasswords();
