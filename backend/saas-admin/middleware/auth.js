// Authentication middleware for SAAS Admin
const db = require('../config/database');

function requireAuth(req, res, next) {
    if (!req.session.user) {
        if (req.xhr || req.headers.accept.indexOf('json') > -1) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        return res.redirect('/login');
    }
    next();
}

function requireRole(roles) {
    return (req, res, next) => {
        if (!req.session.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        
        const userRole = req.session.user.role;
        if (!roles.includes(userRole)) {
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions'
            });
        }
        
        next();
    };
}

// Enhanced auth middleware with user data refresh
async function authMiddleware(req, res, next) {
    try {
        if (!req.session.user) {
            if (req.xhr || req.headers.accept.indexOf('json') > -1) {
                return res.status(401).json({
                    success: false,
                    message: 'Authentication required'
                });
            }
            return res.redirect('/login');
        }

        // Refresh user data from database
        const user = await db.findOne('users', { id: req.session.user.id });
        if (!user || user.status !== 'active') {
            req.session.destroy();
            if (req.xhr || req.headers.accept.indexOf('json') > -1) {
                return res.status(401).json({
                    success: false,
                    message: 'User account is not active'
                });
            }
            return res.redirect('/login');
        }

        // Update session with fresh user data
        req.session.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            role: user.role,
            avatar: user.avatar,
            farmName: user.farmName
        };

        // Make user available in views
        res.locals.user = req.session.user;
        res.locals.isAuthenticated = true;

        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        req.session.destroy();
        if (req.xhr || req.headers.accept.indexOf('json') > -1) {
            return res.status(500).json({
                success: false,
                message: 'Authentication error'
            });
        }
        res.redirect('/auth/login');
    }
}

// Optional auth - doesn't redirect if not authenticated
function optionalAuth(req, res, next) {
    if (req.session.user) {
        res.locals.user = req.session.user;
        res.locals.isAuthenticated = true;
    } else {
        res.locals.user = null;
        res.locals.isAuthenticated = false;
    }
    next();
}

module.exports = {
    requireAuth,
    requireRole,
    authMiddleware,
    optionalAuth
};