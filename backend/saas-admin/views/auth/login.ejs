<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %>
  </title>

  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <style>
    body {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e3a8a 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
    }

    .login-container {
      max-width: 450px;
      margin: 0 auto;
    }

    .login-card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.95);
    }

    .login-header {
      text-align: center;
      padding: 2rem 2rem 1rem;
    }

    .login-header img {
      width: 80px;
      height: 80px;
      margin-bottom: 1rem;
    }

    .login-header h3 {
      color: #333;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .login-header p {
      color: #666;
      margin: 0;
    }

    .login-body {
      padding: 0 2rem 2rem;
    }

    .form-floating {
      margin-bottom: 1.5rem;
    }

    .form-control {
      border-radius: 10px;
      border: 2px solid #e1e5e9;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      border-color: #2563eb;
      box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    .btn-login {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border: none;
      border-radius: 10px;
      padding: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-login:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
    }

    .btn-login:disabled {
      opacity: 0.7;
      transform: none;
    }

    .alert {
      border-radius: 10px;
      border: none;
    }

    .loading-spinner {
      display: none;
    }

    .loading .loading-spinner {
      display: inline-block;
    }

    .loading .btn-text {
      display: none;
    }

    .footer-link {
      text-align: center;
      margin-top: 2rem;
    }

    .footer-link a {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-link a:hover {
      color: white;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="login-container">
      <div class="card login-card">
        <div class="login-header">
          <img src="/img/logo.png" alt="智慧养鹅" id="brandLogo">
          <h3>智慧养鹅SAAS</h3>
          <p>管理平台登录</p>
        </div>

        <div class="login-body">
          <% if (typeof error !=='undefined' && error) { %>
            <div class="alert alert-danger" role="alert">
              <i class="fas fa-exclamation-triangle me-2"></i>
              <%= error %>
            </div>
            <% } %>

              <form id="loginForm">
                <div class="form-floating">
                  <input type="text" class="form-control" id="username" name="username" placeholder="用户名/邮箱" required
                    autocomplete="username">
                  <label for="username">
                    <i class="fas fa-user me-2"></i>用户名/邮箱
                  </label>
                </div>

                <div class="form-floating">
                  <input type="password" class="form-control" id="password" name="password" placeholder="密码" required
                    autocomplete="current-password">
                  <label for="password">
                    <i class="fas fa-lock me-2"></i>密码
                  </label>
                </div>

                <div class="mb-3 form-check">
                  <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                  <label class="form-check-label" for="rememberMe">
                    记住我
                  </label>
                </div>

                <button type="submit" class="btn btn-login btn-primary w-100 text-white">
                  <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"
                    aria-hidden="true"></span>
                  <span class="btn-text">登录</span>
                </button>
              </form>

              <div class="text-center mt-3">
                <small class="text-muted">
                  默认账号: admin / admin123
                </small>
              </div>
        </div>
      </div>

      <div class="footer-link">
        <small>
          <a href="#">忘记密码？</a> ·
          <a href="#">技术支持</a>
        </small>
      </div>
    </div>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

  <script>
    document.getElementById('loginForm').addEventListener('submit', async function (e) {
      e.preventDefault();

      const form = this;
      const submitBtn = form.querySelector('button[type="submit"]');
      const formData = new FormData(form);

      // Show loading state
      submitBtn.disabled = true;
      submitBtn.classList.add('loading');

      try {
        const response = await axios.post('/auth/login', {
          username: formData.get('username'),
          password: formData.get('password'),
          rememberMe: formData.get('rememberMe')
        });

        console.log('Login response:', response.data);

        if (response.data.success) {
          console.log('Login successful, redirecting to:', response.data.redirect || '/dashboard');
          // Redirect to dashboard
          window.location.href = response.data.redirect || '/dashboard';
        } else {
          showError(response.data.message || '登录失败');
        }

      } catch (error) {
        console.error('Login error:', error);

        let errorMessage = '登录失败，请稍后重试';

        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        showError(errorMessage);
      } finally {
        // Hide loading state
        submitBtn.disabled = false;
        submitBtn.classList.remove('loading');
      }
    });

    function showError(message) {
      // Remove existing alerts
      const existingAlert = document.querySelector('.alert');
      if (existingAlert) {
        existingAlert.remove();
      }

      // Create new alert
      const alertDiv = document.createElement('div');
      alertDiv.className = 'alert alert-danger';
      alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;

      // Insert at the top of login body
      const loginBody = document.querySelector('.login-body');
      loginBody.insertBefore(alertDiv, loginBody.firstChild);
    }

    // Focus on username field when page loads
    document.addEventListener('DOMContentLoaded', function () {
      document.getElementById('username').focus();

      // Handle logo error
      const brandLogo = document.getElementById('brandLogo');
      if (brandLogo) {
        brandLogo.addEventListener('error', function () {
          this.style.display = 'none';
        });
      }
    });

    // Allow Enter key to submit form
    document.addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
      }
    });
  </script>
</body>

</html>