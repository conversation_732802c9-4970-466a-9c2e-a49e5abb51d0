-- =====================================================
-- 智慧养鹅SAAS平台 - 统一数据库架构
-- 版本: v2.0
-- 创建时间: 2024-01-28
-- 说明: 统一所有表结构，规范字段命名，建立完整的数据关系
-- =====================================================

-- 删除现有数据库并重新创建
DROP DATABASE IF EXISTS smart_goose_saas_platform;
CREATE DATABASE smart_goose_saas_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smart_goose_saas_platform;

-- =====================================================
-- 1. 平台管理员表 (统一版本)
-- =====================================================
CREATE TABLE platform_admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码(bcrypt加密)',
    name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('super_admin', 'admin', 'operator', 'support') DEFAULT 'admin' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    avatar VARCHAR(500) COMMENT '头像URL',
    last_login DATETIME COMMENT '最后登录时间',
    permissions JSON COMMENT '权限配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='平台管理员表';

-- =====================================================
-- 2. 租户表
-- =====================================================
CREATE TABLE tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_code VARCHAR(50) NOT NULL UNIQUE COMMENT '租户编码',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    company_name VARCHAR(200) COMMENT '公司名称',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '地址',
    subscription_plan_id BIGINT COMMENT '订阅计划ID',
    status ENUM('active', 'inactive', 'suspended', 'expired') DEFAULT 'active' COMMENT '状态',
    trial_end_date DATE COMMENT '试用结束日期',
    subscription_start_date DATE COMMENT '订阅开始日期',
    subscription_end_date DATE COMMENT '订阅结束日期',
    max_users INT DEFAULT 10 COMMENT '最大用户数',
    max_flocks INT DEFAULT 50 COMMENT '最大鹅群数',
    storage_limit BIGINT DEFAULT 1073741824 COMMENT '存储限制(字节)',
    api_rate_limit INT DEFAULT 1000 COMMENT 'API调用限制/小时',
    custom_domain VARCHAR(100) COMMENT '自定义域名',
    logo_url VARCHAR(500) COMMENT 'Logo URL',
    theme_config JSON COMMENT '主题配置',
    feature_flags JSON COMMENT '功能开关',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_tenant_code (tenant_code),
    INDEX idx_status (status),
    INDEX idx_subscription_plan (subscription_plan_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='租户表';

-- =====================================================
-- 3. 订阅计划表
-- =====================================================
CREATE TABLE subscription_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_code VARCHAR(50) NOT NULL UNIQUE COMMENT '计划编码',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    description TEXT COMMENT '计划描述',
    price_monthly DECIMAL(10,2) DEFAULT 0.00 COMMENT '月付价格',
    price_yearly DECIMAL(10,2) DEFAULT 0.00 COMMENT '年付价格',
    max_users INT DEFAULT 10 COMMENT '最大用户数',
    max_flocks INT DEFAULT 50 COMMENT '最大鹅群数',
    storage_limit BIGINT DEFAULT 1073741824 COMMENT '存储限制(字节)',
    api_rate_limit INT DEFAULT 1000 COMMENT 'API调用限制/小时',
    features JSON COMMENT '功能列表',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_plan_code (plan_code),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB COMMENT='订阅计划表';

-- =====================================================
-- 4. 租户用户表
-- =====================================================
CREATE TABLE tenant_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码(bcrypt加密)',
    name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('admin', 'manager', 'finance', 'employee') DEFAULT 'employee' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending' COMMENT '状态',
    avatar VARCHAR(500) COMMENT '头像URL',
    department VARCHAR(50) COMMENT '部门',
    position VARCHAR(50) COMMENT '职位',
    permissions JSON COMMENT '权限配置',
    last_login DATETIME COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_tenant_username (tenant_id, username),
    UNIQUE KEY uk_tenant_email (tenant_id, email),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_role (role),
    INDEX idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='租户用户表';

-- =====================================================
-- 5. 今日鹅价表
-- =====================================================
CREATE TABLE goose_prices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    price_date DATE NOT NULL COMMENT '价格日期',
    region VARCHAR(50) NOT NULL COMMENT '地区',
    breed VARCHAR(50) NOT NULL COMMENT '品种',
    weight_range VARCHAR(20) COMMENT '重量范围',
    price_per_kg DECIMAL(8,2) NOT NULL COMMENT '每公斤价格',
    market_name VARCHAR(100) COMMENT '市场名称',
    source VARCHAR(100) COMMENT '数据来源',
    trend ENUM('up', 'down', 'stable') DEFAULT 'stable' COMMENT '价格趋势',
    notes TEXT COMMENT '备注',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_by BIGINT COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_date_region_breed (price_date, region, breed, weight_range),
    INDEX idx_price_date (price_date),
    INDEX idx_region (region),
    INDEX idx_breed (breed),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (created_by) REFERENCES platform_admins(id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='今日鹅价表';

-- =====================================================
-- 6. 平台公告表
-- =====================================================
CREATE TABLE platform_announcements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type ENUM('system', 'maintenance', 'feature', 'promotion') DEFAULT 'system' COMMENT '公告类型',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    target_audience ENUM('all', 'admins', 'tenants', 'users') DEFAULT 'all' COMMENT '目标受众',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
    publish_at DATETIME COMMENT '发布时间',
    expire_at DATETIME COMMENT '过期时间',
    is_sticky BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_publish_at (publish_at),
    INDEX idx_is_sticky (is_sticky),
    FOREIGN KEY (created_by) REFERENCES platform_admins(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='平台公告表';

-- =====================================================
-- 外键约束
-- =====================================================
ALTER TABLE tenants ADD FOREIGN KEY (subscription_plan_id) REFERENCES subscription_plans(id) ON DELETE SET NULL;

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入默认订阅计划
INSERT INTO subscription_plans (plan_code, display_name, description, price_monthly, price_yearly, max_users, max_flocks, storage_limit, api_rate_limit, features, sort_order) VALUES
('free', '免费版', '适合个人用户和小型养殖场', 0.00, 0.00, 5, 10, 536870912, 100, '["基础鹅群管理", "健康记录", "生产记录"]', 1),
('basic', '基础版', '适合中小型养殖场', 99.00, 999.00, 20, 50, 2147483648, 500, '["完整鹅群管理", "健康记录", "生产记录", "财务管理", "报表分析"]', 2),
('professional', '专业版', '适合大型养殖场', 299.00, 2999.00, 100, 200, 10737418240, 2000, '["全功能", "高级报表", "API接口", "数据导出", "技术支持"]', 3),
('enterprise', '企业版', '适合企业级用户', 999.00, 9999.00, 500, 1000, 53687091200, 10000, '["全功能", "定制开发", "专属服务", "私有部署", "7x24技术支持"]', 4);

-- 插入默认平台管理员
INSERT INTO platform_admins (username, email, password, name, role, permissions) VALUES 
('super_admin', '<EMAIL>', '$2b$10$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY', '超级管理员', 'super_admin', '{"all": true}'),
('admin', '<EMAIL>', '$2b$10$rQZ8kHWKtGY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY5uY', '系统管理员', 'admin', '{"tenants": "*", "users": "*", "reports": "*"}');

-- 插入示例租户
INSERT INTO tenants (tenant_code, tenant_name, company_name, contact_person, contact_phone, contact_email, subscription_plan_id, max_users, max_flocks) VALUES
('demo001', '示例养殖场', '智慧养鹅示例公司', '张三', '13800138000', '<EMAIL>', 2, 20, 50),
('test001', '测试租户', '测试公司', '李四', '13900139000', '<EMAIL>', 1, 5, 10);

-- 插入示例今日鹅价
INSERT INTO goose_prices (price_date, region, breed, weight_range, price_per_kg, market_name, source, trend, created_by) VALUES
(CURDATE(), '江苏', '白鹅', '3-4kg', 18.50, '南京农贸市场', '市场调研', 'up', 1),
(CURDATE(), '山东', '灰鹅', '4-5kg', 19.20, '济南批发市场', '市场调研', 'stable', 1),
(CURDATE(), '广东', '狮头鹅', '5-6kg', 22.80, '广州家禽市场', '市场调研', 'up', 1);

-- 插入示例公告
INSERT INTO platform_announcements (title, content, type, priority, status, publish_at, created_by) VALUES
('系统升级通知', '系统将于今晚22:00-24:00进行升级维护，期间可能影响正常使用，请提前做好准备。', 'maintenance', 'high', 'published', NOW(), 1),
('新功能发布', '智慧养鹅平台新增AI健康诊断功能，欢迎体验使用！', 'feature', 'normal', 'published', NOW(), 1);

-- =====================================================
-- 创建视图和索引优化
-- =====================================================

-- 租户统计视图
CREATE VIEW tenant_stats AS
SELECT 
    t.id,
    t.tenant_name,
    t.status,
    sp.display_name as plan_name,
    COUNT(tu.id) as user_count,
    t.max_users,
    t.created_at
FROM tenants t
LEFT JOIN subscription_plans sp ON t.subscription_plan_id = sp.id
LEFT JOIN tenant_users tu ON t.id = tu.tenant_id AND tu.status = 'active'
GROUP BY t.id;

-- 今日价格统计视图
CREATE VIEW daily_price_stats AS
SELECT 
    price_date,
    COUNT(*) as total_records,
    AVG(price_per_kg) as avg_price,
    MIN(price_per_kg) as min_price,
    MAX(price_per_kg) as max_price,
    COUNT(CASE WHEN trend = 'up' THEN 1 END) as up_count,
    COUNT(CASE WHEN trend = 'down' THEN 1 END) as down_count,
    COUNT(CASE WHEN trend = 'stable' THEN 1 END) as stable_count
FROM goose_prices 
WHERE is_active = TRUE
GROUP BY price_date
ORDER BY price_date DESC;

-- =====================================================
-- 完成
-- =====================================================
SELECT '数据库初始化完成！' as message;
