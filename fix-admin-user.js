const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function fixAdminUser() {
    let connection;
    
    try {
        // 数据库连接配置
        const dbConfig = {
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: '', // 空密码
            database: 'smart_goose_saas_platform',
            charset: 'utf8mb4'
        };
        
        console.log('🔗 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');
        
        // 1. 创建 platform_admins 表（如果不存在）
        console.log('📋 创建 platform_admins 表...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS platform_admins (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
                email VARCHAR(100) UNIQUE COMMENT '邮箱',
                password VARCHAR(255) NOT NULL COMMENT '密码',
                name VARCHAR(100) COMMENT '真实姓名',
                phone VARCHAR(20) COMMENT '手机号',
                role ENUM('super_admin', 'admin', 'operator', 'support') DEFAULT 'admin' COMMENT '角色',
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
                avatar VARCHAR(500) COMMENT '头像URL',
                last_login DATETIME COMMENT '最后登录时间',
                permissions JSON COMMENT '权限列表',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_role (role),
                INDEX idx_status (status)
            ) ENGINE=InnoDB COMMENT='平台管理员表';
        `);
        console.log('✅ platform_admins 表创建成功');
        
        // 2. 生成密码哈希
        console.log('🔐 生成密码哈希...');
        const passwordHash = await bcrypt.hash('admin123', 10);
        console.log('✅ 密码哈希生成成功');
        
        // 3. 插入管理员用户
        console.log('👤 创建管理员用户...');
        await connection.execute(`
            INSERT INTO platform_admins (username, email, password, name, role, status, permissions) VALUES 
            (?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                password = VALUES(password),
                name = VALUES(name),
                role = VALUES(role),
                status = VALUES(status),
                permissions = VALUES(permissions),
                updated_at = CURRENT_TIMESTAMP
        `, [
            'super_admin',
            '<EMAIL>', 
            passwordHash,
            '超级管理员',
            'super_admin',
            'active',
            JSON.stringify({
                "all": true,
                "platform": "*",
                "tenants": "*", 
                "users": "*",
                "system": "*"
            })
        ]);
        
        console.log('✅ 超级管理员用户创建成功');
        
        // 4. 验证用户创建
        console.log('🔍 验证用户创建...');
        const [users] = await connection.execute(
            'SELECT id, username, email, name, role, status FROM platform_admins WHERE username = ?',
            ['super_admin']
        );
        
        if (users.length > 0) {
            console.log('✅ 用户验证成功:');
            console.log('   用户名:', users[0].username);
            console.log('   邮箱:', users[0].email);
            console.log('   姓名:', users[0].name);
            console.log('   角色:', users[0].role);
            console.log('   状态:', users[0].status);
        } else {
            console.log('❌ 用户验证失败');
        }
        
        // 5. 测试密码验证
        console.log('🔐 测试密码验证...');
        const isValid = await bcrypt.compare('admin123', passwordHash);
        console.log('✅ 密码验证结果:', isValid ? '成功' : '失败');
        
        console.log('\n🎉 管理员用户修复完成！');
        console.log('📝 登录信息:');
        console.log('   URL: http://localhost:4001');
        console.log('   用户名: super_admin');
        console.log('   密码: admin123');
        
    } catch (error) {
        console.error('❌ 修复失败:', error.message);
        console.error('详细错误:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 运行修复脚本
fixAdminUser();
