/**
 * 简化版SAAS管理后台管理员账号创建脚本
 * 不依赖bcrypt，使用简单哈希用于测试
 */

const path = require('path');
const crypto = require('crypto');

// 使用SAAS管理后台的数据库连接
const saasAdminPath = path.join(__dirname, '../backend/saas-admin');
const db = require(path.join(saasAdminPath, 'config/database'));

// 简单的密码哈希函数（仅用于测试）
function simpleHash(password) {
    return crypto.createHash('sha256').update(password + 'salt').digest('hex');
}

async function createTestAdmin() {
    try {
        console.log('🚀 SAAS管理后台测试管理员创建工具');
        console.log('=' .repeat(50));
        
        console.log('🔗 连接数据库...');
        await db.testConnection();
        console.log('✅ 数据库连接成功');

        // 检查users表是否存在
        console.log('\n📋 检查数据库表结构...');
        const [tables] = await db.execute("SHOW TABLES LIKE 'users'");
        
        if (tables.length === 0) {
            console.log('❌ users表不存在，尝试创建基础表结构...');
            
            // 创建基础users表
            const createTableSQL = `
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    name VARCHAR(100),
                    email VARCHAR(100),
                    role ENUM('super_admin', 'platform_admin', 'admin', 'user') DEFAULT 'user',
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            `;
            
            await db.execute(createTableSQL);
            console.log('✅ users表创建成功');
        }

        // 检查现有用户
        console.log('\n👥 检查现有用户...');
        const [existingUsers] = await db.execute('SELECT id, username, role FROM users LIMIT 10');
        
        if (existingUsers.length > 0) {
            console.log('📊 现有用户列表:');
            existingUsers.forEach(user => {
                console.log(`   - ID: ${user.id}, 用户名: ${user.username}, 角色: ${user.role}`);
            });
        } else {
            console.log('📊 数据库中暂无用户');
        }

        // 检查是否已有管理员账号
        const [adminUsers] = await db.execute(
            "SELECT * FROM users WHERE role IN ('super_admin', 'platform_admin', 'admin') LIMIT 1"
        );

        if (adminUsers.length > 0) {
            console.log(`\n✅ 已存在管理员账号: ${adminUsers[0].username}`);
            console.log('🔑 可以尝试使用以下凭据登录:');
            console.log(`   用户名: ${adminUsers[0].username}`);
            console.log(`   密码: admin123 (如果是默认密码)`);
            return true;
        }

        // 创建多个测试管理员账号
        console.log('\n🔧 创建测试管理员账号...');
        
        const testAdmins = [
            {
                username: 'super_admin',
                password: simpleHash('admin123'),
                name: '超级管理员',
                email: '<EMAIL>',
                role: 'super_admin'
            },
            {
                username: 'admin',
                password: simpleHash('admin123'),
                name: '管理员',
                email: '<EMAIL>',
                role: 'admin'
            },
            {
                username: 'platform_admin',
                password: simpleHash('admin123'),
                name: '平台管理员',
                email: '<EMAIL>',
                role: 'platform_admin'
            }
        ];

        let createdCount = 0;
        
        for (const admin of testAdmins) {
            try {
                const insertQuery = `
                    INSERT INTO users (username, password, name, email, role, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
                `;

                const [result] = await db.execute(insertQuery, [
                    admin.username,
                    admin.password,
                    admin.name,
                    admin.email,
                    admin.role
                ]);

                if (result.insertId) {
                    console.log(`✅ 创建管理员: ${admin.username} (${admin.role})`);
                    createdCount++;
                }
            } catch (error) {
                if (error.code === 'ER_DUP_ENTRY') {
                    console.log(`⚠️  管理员 ${admin.username} 已存在，跳过`);
                } else {
                    console.log(`❌ 创建管理员 ${admin.username} 失败: ${error.message}`);
                }
            }
        }

        if (createdCount > 0) {
            console.log(`\n🎉 成功创建 ${createdCount} 个管理员账号!`);
            console.log('🔑 登录凭据 (任选其一):');
            testAdmins.forEach(admin => {
                console.log(`   用户名: ${admin.username}, 密码: admin123, 角色: ${admin.role}`);
            });
            return true;
        } else {
            console.log('\n⚠️  没有创建新的管理员账号，可能都已存在');
            return true;
        }

    } catch (error) {
        console.error('❌ 操作失败:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 建议: 请确保MySQL服务正在运行');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 建议: 请检查数据库连接凭据');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('💡 建议: 请确保数据库 smart_goose_saas_platform 存在');
        }
        
        return false;
    }
}

// 主函数
async function main() {
    const success = await createTestAdmin();
    
    if (success) {
        console.log('\n🎯 操作完成！现在可以运行测试了');
        process.exit(0);
    } else {
        console.log('\n❌ 操作失败，请检查错误信息');
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = { createTestAdmin };
