/**
 * 智慧养鹅SAAS管理后台 - 修复版详细模块和按钮测试
 * 使用正确的CSS选择器进行UI交互测试
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class FixedModuleButtonTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.context = null;
    this.baseURL = 'http://localhost:4000';
    this.testResults = [];
    this.screenshots = [];
    
    // 测试凭据
    this.credentials = {
      username: 'super_admin',
      password: 'admin123'
    };
    
    // 修正后的模块配置 - 基于实际的HTML模板
    this.modules = {
      dashboard: {
        name: '平台仪表盘',
        url: '/dashboard',
        buttons: [
          { selector: '.btn-tool', name: '工具按钮', action: 'click' },
          { selector: '.small-box-footer', name: '查看详情链接', action: 'click' },
          { selector: '.card-tools .btn', name: '卡片工具按钮', action: 'click' }
        ],
        elements: [
          { selector: '.small-box', name: '统计卡片', check: 'visible' },
          { selector: '.info-box', name: '信息框', check: 'visible' },
          { selector: '.card', name: '卡片容器', check: 'visible' },
          { selector: '.table', name: '数据表格', check: 'visible' },
          { selector: '.chart-container, #priceChart', name: '图表容器', check: 'exists' }
        ]
      },
      tenants: {
        name: '租户管理',
        url: '/tenants',
        buttons: [
          { selector: '.btn-primary', name: '主要操作按钮', action: 'click' },
          { selector: '.btn-success', name: '导出数据按钮', action: 'click' },
          { selector: '.btn-warning', name: '订阅管理按钮', action: 'click' },
          { selector: '.btn-info', name: '使用统计按钮', action: 'click' },
          { selector: '.btn-sm', name: '小按钮操作', action: 'click' }
        ],
        elements: [
          { selector: '.table-responsive', name: '响应式表格', check: 'visible' },
          { selector: '.table', name: '租户列表表格', check: 'visible' },
          { selector: '.card-header', name: '卡片头部', check: 'visible' },
          { selector: '.btn-group', name: '按钮组', check: 'exists' },
          { selector: 'input[type="checkbox"]', name: '复选框', check: 'exists' }
        ]
      },
      goosePrices: {
        name: '今日鹅价管理',
        url: '/goose-prices',
        buttons: [
          { selector: '.btn-primary', name: '主要操作按钮', action: 'click' },
          { selector: '.btn-success', name: '成功操作按钮', action: 'click' },
          { selector: '.btn-warning', name: '警告操作按钮', action: 'click' },
          { selector: '.btn-info', name: '信息操作按钮', action: 'click' },
          { selector: '.btn-sm', name: '小按钮', action: 'click' }
        ],
        elements: [
          { selector: '.table', name: '价格列表表格', check: 'visible' },
          { selector: '#priceChart', name: '价格走势图', check: 'exists' },
          { selector: '.badge', name: '价格变化标签', check: 'visible' },
          { selector: '.card', name: '卡片容器', check: 'visible' },
          { selector: '.fw-bold', name: '价格显示', check: 'visible' }
        ]
      },
      announcements: {
        name: '平台公告管理',
        url: '/announcements',
        buttons: [
          { selector: '.btn-primary', name: '创建公告按钮', action: 'click' },
          { selector: '.btn-success', name: '发布按钮', action: 'click' },
          { selector: '.btn-warning', name: '编辑按钮', action: 'click' },
          { selector: '.btn-danger', name: '删除按钮', action: 'click' },
          { selector: '.btn-info', name: '预览按钮', action: 'click' }
        ],
        elements: [
          { selector: '.card', name: '公告卡片', check: 'visible' },
          { selector: '.table', name: '公告列表', check: 'exists' },
          { selector: '.form-control', name: '表单控件', check: 'exists' },
          { selector: '.badge', name: '状态标签', check: 'exists' }
        ]
      },
      knowledge: {
        name: '知识库管理',
        url: '/knowledge',
        buttons: [
          { selector: '.btn-primary', name: '添加文章按钮', action: 'click' },
          { selector: '.btn-success', name: '保存按钮', action: 'click' },
          { selector: '.btn-warning', name: '编辑按钮', action: 'click' },
          { selector: '.btn-danger', name: '删除按钮', action: 'click' },
          { selector: '.btn-info', name: '分类管理按钮', action: 'click' }
        ],
        elements: [
          { selector: '.card', name: '知识卡片', check: 'visible' },
          { selector: '.list-group', name: '知识列表', check: 'exists' },
          { selector: '.nav-tabs', name: '分类标签', check: 'exists' },
          { selector: '.form-control', name: '搜索框', check: 'exists' }
        ]
      },
      mall: {
        name: '商城模块管理',
        url: '/mall',
        buttons: [
          { selector: '.btn-primary', name: '添加商品按钮', action: 'click' },
          { selector: '.btn-success', name: '上架按钮', action: 'click' },
          { selector: '.btn-warning', name: '编辑商品按钮', action: 'click' },
          { selector: '.btn-danger', name: '删除商品按钮', action: 'click' },
          { selector: '.btn-info', name: '订单管理按钮', action: 'click' }
        ],
        elements: [
          { selector: '.card', name: '商品卡片', check: 'visible' },
          { selector: '.row', name: '商品网格', check: 'visible' },
          { selector: '.table', name: '订单表格', check: 'exists' },
          { selector: '.badge', name: '状态标签', check: 'exists' }
        ]
      },
      aiConfig: {
        name: 'AI大模型配置',
        url: '/ai-config',
        buttons: [
          { selector: '.btn-primary', name: '保存配置按钮', action: 'click' },
          { selector: '.btn-success', name: '测试连接按钮', action: 'click' },
          { selector: '.btn-warning', name: '重置配置按钮', action: 'click' },
          { selector: '.btn-info', name: 'API测试按钮', action: 'click' },
          { selector: '.btn-secondary', name: '取消按钮', action: 'click' }
        ],
        elements: [
          { selector: '.form-control', name: '配置表单', check: 'visible' },
          { selector: '.card', name: '配置卡片', check: 'visible' },
          { selector: '.alert', name: '提示信息', check: 'exists' },
          { selector: '.form-group', name: '表单组', check: 'visible' }
        ]
      },
      settings: {
        name: '系统设置',
        url: '/settings',
        buttons: [
          { selector: '.btn-primary', name: '保存设置按钮', action: 'click' },
          { selector: '.btn-success', name: '应用设置按钮', action: 'click' },
          { selector: '.btn-warning', name: '重置设置按钮', action: 'click' },
          { selector: '.btn-info', name: '备份数据按钮', action: 'click' },
          { selector: '.btn-danger', name: '清除缓存按钮', action: 'click' }
        ],
        elements: [
          { selector: '.nav-tabs', name: '设置标签页', check: 'visible' },
          { selector: '.form-control', name: '设置表单', check: 'visible' },
          { selector: '.card', name: '设置卡片', check: 'visible' },
          { selector: '.table', name: '系统信息表格', check: 'exists' }
        ]
      }
    };
  }

  // 记录测试结果
  logResult(module, item, type, success, message, details = null) {
    const result = {
      module,
      item,
      type,
      success,
      message,
      details,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} [${module}] ${type}: ${item} - ${message}`);
    if (details) {
      console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
    }
  }

  // 初始化浏览器
  async initBrowser() {
    console.log('🚀 启动Playwright浏览器...');
    try {
      this.browser = await chromium.launch({
        headless: false,
        slowMo: 300,
        args: ['--start-maximized']
      });
      
      this.context = await this.browser.newContext({
        viewport: { width: 1920, height: 1080 }
      });
      
      this.page = await this.context.newPage();
      this.page.setDefaultTimeout(8000);
      this.page.setDefaultNavigationTimeout(12000);
      
      console.log('✅ 浏览器启动成功');
      return true;
    } catch (error) {
      console.error('❌ 浏览器启动失败:', error.message);
      return false;
    }
  }

  // 登录系统
  async login() {
    console.log('\n🔑 执行登录流程...');
    try {
      await this.page.goto(this.baseURL);
      await this.page.waitForLoadState('networkidle');
      
      // 检查是否已经登录
      const currentUrl = this.page.url();
      if (currentUrl.includes('/dashboard')) {
        console.log('✅ 已经登录，跳过登录步骤');
        return true;
      }
      
      // 等待登录页面加载
      await this.page.waitForSelector('input[name="username"], #username', { timeout: 5000 });
      
      // 填写登录信息
      const usernameSelector = await this.page.$('input[name="username"]') ? 'input[name="username"]' : '#username';
      const passwordSelector = await this.page.$('input[name="password"]') ? 'input[name="password"]' : '#password';
      
      await this.page.fill(usernameSelector, this.credentials.username);
      await this.page.fill(passwordSelector, this.credentials.password);
      
      // 点击登录按钮
      const loginButton = await this.page.$('button[type="submit"]') || await this.page.$('.login-btn') || await this.page.$('input[type="submit"]');
      if (loginButton) {
        await loginButton.click();
      } else {
        await this.page.press(passwordSelector, 'Enter');
      }
      
      // 等待登录完成
      await this.page.waitForURL('**/dashboard', { timeout: 10000 });
      
      console.log('✅ 登录成功');
      return true;
    } catch (error) {
      console.error('❌ 登录失败:', error.message);
      return false;
    }
  }

  // 截图
  async takeScreenshot(name, fullPage = false) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `fixed-screenshot-${name}-${timestamp}.png`;
      const filepath = path.join(__dirname, 'screenshots', filename);
      
      const screenshotDir = path.dirname(filepath);
      if (!fs.existsSync(screenshotDir)) {
        fs.mkdirSync(screenshotDir, { recursive: true });
      }
      
      await this.page.screenshot({ 
        path: filepath, 
        fullPage 
      });
      
      this.screenshots.push({
        name,
        filename,
        filepath,
        timestamp
      });
      
      console.log(`📸 截图保存: ${filename}`);
      return filepath;
    } catch (error) {
      console.error('❌ 截图失败:', error.message);
      return null;
    }
  }

  // 测试单个模块
  async testModule(moduleKey, moduleConfig) {
    console.log(`\n🔍 测试模块: ${moduleConfig.name}`);
    console.log('-'.repeat(50));

    try {
      // 导航到模块页面
      await this.page.goto(`${this.baseURL}${moduleConfig.url}`);
      await this.page.waitForLoadState('networkidle');

      // 截图记录页面状态
      await this.takeScreenshot(`${moduleKey}-initial`);

      // 检查页面是否正确加载
      const currentUrl = this.page.url();
      if (currentUrl.includes(moduleConfig.url)) {
        this.logResult(moduleConfig.name, '页面导航', 'navigation', true, '成功导航到模块页面');
      } else {
        this.logResult(moduleConfig.name, '页面导航', 'navigation', false, `导航失败，当前URL: ${currentUrl}`);
        return;
      }

      // 等待页面内容加载
      await this.page.waitForTimeout(3000);

      // 测试页面元素
      await this.testModuleElements(moduleKey, moduleConfig);

      // 测试按钮功能
      await this.testModuleButtons(moduleKey, moduleConfig);

    } catch (error) {
      this.logResult(moduleConfig.name, '模块测试', 'module', false, `模块测试失败: ${error.message}`);
      console.error(`❌ 模块 ${moduleConfig.name} 测试失败:`, error.message);
    }
  }

  // 测试模块元素
  async testModuleElements(moduleKey, moduleConfig) {
    console.log(`📋 测试 ${moduleConfig.name} 页面元素...`);

    for (const element of moduleConfig.elements) {
      try {
        const elements = await this.page.$$(element.selector);

        if (element.check === 'visible') {
          let visibleCount = 0;
          for (const el of elements) {
            if (await el.isVisible()) {
              visibleCount++;
            }
          }

          const success = visibleCount > 0;
          this.logResult(
            moduleConfig.name,
            element.name,
            'element',
            success,
            success ? `找到${visibleCount}个可见元素` : '元素不可见或不存在'
          );
        } else if (element.check === 'exists') {
          const exists = elements.length > 0;
          this.logResult(
            moduleConfig.name,
            element.name,
            'element',
            exists,
            exists ? `找到${elements.length}个元素` : '元素不存在'
          );
        }
      } catch (error) {
        this.logResult(
          moduleConfig.name,
          element.name,
          'element',
          false,
          `元素检查失败: ${error.message}`
        );
      }
    }
  }

  // 测试模块按钮
  async testModuleButtons(moduleKey, moduleConfig) {
    console.log(`🔘 测试 ${moduleConfig.name} 按钮功能...`);

    for (const button of moduleConfig.buttons) {
      try {
        // 查找按钮元素
        const buttonElements = await this.page.$$(button.selector);

        if (buttonElements.length === 0) {
          this.logResult(
            moduleConfig.name,
            button.name,
            'button',
            false,
            '按钮元素不存在'
          );
          continue;
        }

        // 测试第一个找到的按钮
        const buttonElement = buttonElements[0];

        // 检查按钮是否可见和可点击
        const isVisible = await buttonElement.isVisible();
        const isEnabled = await buttonElement.isEnabled();

        if (!isVisible) {
          this.logResult(
            moduleConfig.name,
            button.name,
            'button',
            false,
            `找到${buttonElements.length}个按钮，但不可见`
          );
          continue;
        }

        if (!isEnabled) {
          this.logResult(
            moduleConfig.name,
            button.name,
            'button',
            false,
            '按钮被禁用'
          );
          continue;
        }

        // 执行按钮操作
        if (button.action === 'click') {
          await this.testButtonClick(moduleKey, moduleConfig, button, buttonElement);
        }

      } catch (error) {
        this.logResult(
          moduleConfig.name,
          button.name,
          'button',
          false,
          `按钮测试失败: ${error.message}`
        );
      }
    }
  }

  // 测试按钮点击
  async testButtonClick(moduleKey, moduleConfig, button, buttonElement) {
    try {
      // 记录点击前的页面状态
      const beforeUrl = this.page.url();

      // 滚动到按钮位置
      await buttonElement.scrollIntoViewIfNeeded();
      await this.page.waitForTimeout(500);

      // 点击按钮
      await buttonElement.click();

      // 等待可能的页面变化
      await this.page.waitForTimeout(2000);

      // 检查点击后的变化
      const afterUrl = this.page.url();
      const urlChanged = beforeUrl !== afterUrl;

      // 检查是否有模态框或弹窗出现
      const modalExists = await this.page.$('.modal.show, .modal.fade.show, .swal2-container') !== null;

      // 检查是否有新的内容加载
      const hasNewContent = await this.checkForNewContent();

      // 检查是否有错误信息
      const hasError = await this.checkForErrors();

      // 截图记录点击后的状态
      await this.takeScreenshot(`${moduleKey}-${button.name.replace(/\s+/g, '-')}-after-click`);

      // 判断按钮点击是否成功
      const clickSuccess = urlChanged || modalExists || hasNewContent || !hasError;

      this.logResult(
        moduleConfig.name,
        button.name,
        'button',
        clickSuccess,
        clickSuccess ? '按钮点击成功，页面有响应' : '按钮点击无明显响应',
        {
          urlChanged,
          modalExists,
          hasNewContent,
          hasError,
          beforeUrl,
          afterUrl
        }
      );

      // 处理点击后的状态
      if (modalExists) {
        await this.closeModalIfExists();
      }

      // 如果URL改变了，导航回原页面
      if (urlChanged && !afterUrl.includes(moduleConfig.url)) {
        await this.page.goto(`${this.baseURL}${moduleConfig.url}`);
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000);
      }

    } catch (error) {
      this.logResult(
        moduleConfig.name,
        button.name,
        'button',
        false,
        `按钮点击测试失败: ${error.message}`
      );
    }
  }

  // 检查新内容
  async checkForNewContent() {
    try {
      const indicators = [
        '.loading', '.spinner', '.progress',
        '.alert', '.toast', '.notification',
        '.success', '.error', '.warning',
        '.swal2-container', '.modal.show'
      ];

      for (const indicator of indicators) {
        const element = await this.page.$(indicator);
        if (element && await element.isVisible()) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  // 检查错误信息
  async checkForErrors() {
    try {
      const errorSelectors = [
        '.alert-danger', '.error', '.text-danger',
        '.is-invalid', '.has-error'
      ];

      for (const selector of errorSelectors) {
        const element = await this.page.$(selector);
        if (element && await element.isVisible()) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  // 关闭模态框
  async closeModalIfExists() {
    try {
      const closeSelectors = [
        '.modal .btn-close', '.modal .close', '.modal [data-bs-dismiss="modal"]',
        '.swal2-close', '.swal2-cancel'
      ];

      for (const selector of closeSelectors) {
        const closeBtn = await this.page.$(selector);
        if (closeBtn && await closeBtn.isVisible()) {
          await closeBtn.click();
          await this.page.waitForTimeout(500);
          return;
        }
      }

      // 尝试按ESC键关闭
      await this.page.keyboard.press('Escape');
      await this.page.waitForTimeout(500);

    } catch (error) {
      console.log('关闭模态框时出错:', error.message);
    }
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始修复版详细模块和按钮测试...');
    console.log('=' .repeat(80));

    try {
      const browserReady = await this.initBrowser();
      if (!browserReady) {
        throw new Error('浏览器初始化失败');
      }

      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('登录失败');
      }

      // 对每个模块进行详细测试
      for (const [moduleKey, moduleConfig] of Object.entries(this.modules)) {
        await this.testModule(moduleKey, moduleConfig);
        await this.page.waitForTimeout(2000);
      }

      return this.generateDetailedReport();
    } catch (error) {
      console.error('❌ 测试执行过程中发生错误:', error);
      this.logResult('系统', '测试执行', 'system', false, `执行错误: ${error.message}`);
      return this.generateDetailedReport();
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔌 浏览器已关闭');
      }
    }
  }

  // 生成详细测试报告
  generateDetailedReport() {
    console.log('\n📊 生成修复版测试报告...');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0;

    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        successRate: `${successRate}%`,
        testDate: new Date().toISOString()
      },
      detailedResults: this.testResults,
      screenshots: this.screenshots
    };

    // 保存报告到文件
    const reportPath = path.join(__dirname, 'fixed-module-button-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n📋 修复版测试报告已保存: ${reportPath}`);
    console.log(`\n🎯 测试总结:`);
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过: ${passedTests}`);
    console.log(`   失败: ${failedTests}`);
    console.log(`   成功率: ${successRate}%`);

    report.reportPath = reportPath;
    return report;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new FixedModuleButtonTester();
  tester.runAllTests().then(report => {
    console.log('\n✅ 修复版详细测试完成！');
    console.log(`📊 测试报告已生成: ${report.reportPath}`);
    process.exit(report.summary.failedTests > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}

module.exports = FixedModuleButtonTester;
