{"summary": {"totalTests": 81, "passedTests": 46, "failedTests": 35, "successRate": "56.79%", "testDate": "2025-08-28T03:53:49.145Z"}, "detailedResults": [{"module": "平台仪表盘", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:52:12.678Z"}, {"module": "平台仪表盘", "item": "统计卡片", "type": "element", "success": true, "message": "找到8个可见元素", "details": null, "timestamp": "2025-08-28T03:52:15.754Z"}, {"module": "平台仪表盘", "item": "信息框", "type": "element", "success": true, "message": "找到10个可见元素", "details": null, "timestamp": "2025-08-28T03:52:15.768Z"}, {"module": "平台仪表盘", "item": "卡片容器", "type": "element", "success": true, "message": "找到4个可见元素", "details": null, "timestamp": "2025-08-28T03:52:15.772Z"}, {"module": "平台仪表盘", "item": "数据表格", "type": "element", "success": true, "message": "找到2个可见元素", "details": null, "timestamp": "2025-08-28T03:52:15.775Z"}, {"module": "平台仪表盘", "item": "图表容器", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:52:15.776Z"}, {"module": "平台仪表盘", "item": "工具按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": true, "hasError": false, "beforeUrl": "http://localhost:4000/dashboard", "afterUrl": "http://localhost:4000/dashboard"}, "timestamp": "2025-08-28T03:52:19.131Z"}, {"module": "平台仪表盘", "item": "查看详情链接", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": true, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/dashboard", "afterUrl": "http://localhost:4000/tenants"}, "timestamp": "2025-08-28T03:52:22.729Z"}, {"module": "平台仪表盘", "item": "卡片工具按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": true, "hasError": false, "beforeUrl": "http://localhost:4000/dashboard", "afterUrl": "http://localhost:4000/dashboard"}, "timestamp": "2025-08-28T03:52:27.722Z"}, {"module": "租户管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:52:30.409Z"}, {"module": "租户管理", "item": "响应式表格", "type": "element", "success": false, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T03:52:33.436Z"}, {"module": "租户管理", "item": "租户列表表格", "type": "element", "success": false, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T03:52:33.438Z"}, {"module": "租户管理", "item": "卡片头部", "type": "element", "success": false, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T03:52:33.439Z"}, {"module": "租户管理", "item": "按钮组", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:52:33.440Z"}, {"module": "租户管理", "item": "复选框", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:52:33.441Z"}, {"module": "租户管理", "item": "主要操作按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": true, "modalExists": false, "hasNewContent": true, "hasError": false, "beforeUrl": "http://localhost:4000/tenants", "afterUrl": "http://localhost:4000/dashboard"}, "timestamp": "2025-08-28T03:52:36.891Z"}, {"module": "租户管理", "item": "导出数据按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:38.471Z"}, {"module": "租户管理", "item": "订阅管理按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:38.473Z"}, {"module": "租户管理", "item": "使用统计按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:38.474Z"}, {"module": "租户管理", "item": "小按钮操作", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:38.476Z"}, {"module": "今日鹅价管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:52:41.291Z"}, {"module": "今日鹅价管理", "item": "价格列表表格", "type": "element", "success": true, "message": "找到2个可见元素", "details": null, "timestamp": "2025-08-28T03:52:44.328Z"}, {"module": "今日鹅价管理", "item": "价格走势图", "type": "element", "success": true, "message": "找到2个元素", "details": null, "timestamp": "2025-08-28T03:52:44.334Z"}, {"module": "今日鹅价管理", "item": "价格变化标签", "type": "element", "success": true, "message": "找到11个可见元素", "details": null, "timestamp": "2025-08-28T03:52:44.358Z"}, {"module": "今日鹅价管理", "item": "卡片容器", "type": "element", "success": true, "message": "找到8个可见元素", "details": null, "timestamp": "2025-08-28T03:52:44.364Z"}, {"module": "今日鹅价管理", "item": "价格显示", "type": "element", "success": true, "message": "找到11个可见元素", "details": null, "timestamp": "2025-08-28T03:52:44.377Z"}, {"module": "今日鹅价管理", "item": "主要操作按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": true, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/goose-prices", "afterUrl": "http://localhost:4000/goose-prices/create"}, "timestamp": "2025-08-28T03:52:47.809Z"}, {"module": "今日鹅价管理", "item": "成功操作按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:47.810Z"}, {"module": "今日鹅价管理", "item": "警告操作按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:47.811Z"}, {"module": "今日鹅价管理", "item": "信息操作按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:47.811Z"}, {"module": "今日鹅价管理", "item": "小按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:47.812Z"}, {"module": "平台公告管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:52:50.528Z"}, {"module": "平台公告管理", "item": "公告卡片", "type": "element", "success": true, "message": "找到1个可见元素", "details": null, "timestamp": "2025-08-28T03:52:53.564Z"}, {"module": "平台公告管理", "item": "公告列表", "type": "element", "success": true, "message": "找到1个元素", "details": null, "timestamp": "2025-08-28T03:52:53.565Z"}, {"module": "平台公告管理", "item": "表单控件", "type": "element", "success": true, "message": "找到2个元素", "details": null, "timestamp": "2025-08-28T03:52:53.568Z"}, {"module": "平台公告管理", "item": "状态标签", "type": "element", "success": true, "message": "找到13个元素", "details": null, "timestamp": "2025-08-28T03:52:53.574Z"}, {"module": "平台公告管理", "item": "创建公告按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": true, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/announcements", "afterUrl": "http://localhost:4000/announcements/create"}, "timestamp": "2025-08-28T03:52:56.955Z"}, {"module": "平台公告管理", "item": "发布按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:56.956Z"}, {"module": "平台公告管理", "item": "编辑按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:56.957Z"}, {"module": "平台公告管理", "item": "删除按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:56.958Z"}, {"module": "平台公告管理", "item": "预览按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:52:56.958Z"}, {"module": "知识库管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:52:59.728Z"}, {"module": "知识库管理", "item": "知识卡片", "type": "element", "success": true, "message": "找到1个可见元素", "details": null, "timestamp": "2025-08-28T03:53:02.761Z"}, {"module": "知识库管理", "item": "知识列表", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:53:02.762Z"}, {"module": "知识库管理", "item": "分类标签", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:53:02.763Z"}, {"module": "知识库管理", "item": "搜索框", "type": "element", "success": true, "message": "找到2个元素", "details": null, "timestamp": "2025-08-28T03:53:02.765Z"}, {"module": "知识库管理", "item": "添加文章按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": true, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/knowledge", "afterUrl": "http://localhost:4000/knowledge/create"}, "timestamp": "2025-08-28T03:53:06.183Z"}, {"module": "知识库管理", "item": "保存按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:06.184Z"}, {"module": "知识库管理", "item": "编辑按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:06.185Z"}, {"module": "知识库管理", "item": "删除按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:06.186Z"}, {"module": "知识库管理", "item": "分类管理按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:06.186Z"}, {"module": "商城模块管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:53:08.940Z"}, {"module": "商城模块管理", "item": "商品卡片", "type": "element", "success": true, "message": "找到10个可见元素", "details": null, "timestamp": "2025-08-28T03:53:12.005Z"}, {"module": "商城模块管理", "item": "商品网格", "type": "element", "success": true, "message": "找到4个可见元素", "details": null, "timestamp": "2025-08-28T03:53:12.011Z"}, {"module": "商城模块管理", "item": "订单表格", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:53:12.011Z"}, {"module": "商城模块管理", "item": "状态标签", "type": "element", "success": true, "message": "找到1个元素", "details": null, "timestamp": "2025-08-28T03:53:12.013Z"}, {"module": "商城模块管理", "item": "添加商品按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": true, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/mall", "afterUrl": "http://localhost:4000/mall/products"}, "timestamp": "2025-08-28T03:53:15.470Z"}, {"module": "商城模块管理", "item": "上架按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:15.471Z"}, {"module": "商城模块管理", "item": "编辑商品按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:15.471Z"}, {"module": "商城模块管理", "item": "删除商品按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:15.472Z"}, {"module": "商城模块管理", "item": "订单管理按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:15.472Z"}, {"module": "AI大模型配置", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:53:18.250Z"}, {"module": "AI大模型配置", "item": "配置表单", "type": "element", "success": true, "message": "找到1个可见元素", "details": null, "timestamp": "2025-08-28T03:53:21.309Z"}, {"module": "AI大模型配置", "item": "配置卡片", "type": "element", "success": true, "message": "找到5个可见元素", "details": null, "timestamp": "2025-08-28T03:53:21.315Z"}, {"module": "AI大模型配置", "item": "提示信息", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T03:53:21.315Z"}, {"module": "AI大模型配置", "item": "表单组", "type": "element", "success": false, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T03:53:21.328Z"}, {"module": "AI大模型配置", "item": "保存配置按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/ai-config", "afterUrl": "http://localhost:4000/ai-config"}, "timestamp": "2025-08-28T03:53:24.635Z"}, {"module": "AI大模型配置", "item": "测试连接按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/ai-config", "afterUrl": "http://localhost:4000/ai-config"}, "timestamp": "2025-08-28T03:53:27.955Z"}, {"module": "AI大模型配置", "item": "重置配置按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T03:53:27.957Z"}, {"module": "AI大模型配置", "item": "API测试按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/ai-config", "afterUrl": "http://localhost:4000/ai-config"}, "timestamp": "2025-08-28T03:53:31.280Z"}, {"module": "AI大模型配置", "item": "取消按钮", "type": "button", "success": false, "message": "找到1个按钮，但不可见", "details": null, "timestamp": "2025-08-28T03:53:31.286Z"}, {"module": "系统设置", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T03:53:34.140Z"}, {"module": "系统设置", "item": "设置标签页", "type": "element", "success": true, "message": "找到1个可见元素", "details": null, "timestamp": "2025-08-28T03:53:37.178Z"}, {"module": "系统设置", "item": "设置表单", "type": "element", "success": true, "message": "找到4个可见元素", "details": null, "timestamp": "2025-08-28T03:53:37.187Z"}, {"module": "系统设置", "item": "设置卡片", "type": "element", "success": true, "message": "找到1个可见元素", "details": null, "timestamp": "2025-08-28T03:53:37.191Z"}, {"module": "系统设置", "item": "系统信息表格", "type": "element", "success": true, "message": "找到1个元素", "details": null, "timestamp": "2025-08-28T03:53:37.195Z"}, {"module": "系统设置", "item": "保存设置按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/settings", "afterUrl": "http://localhost:4000/settings"}, "timestamp": "2025-08-28T03:53:40.502Z"}, {"module": "系统设置", "item": "应用设置按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/settings", "afterUrl": "http://localhost:4000/settings"}, "timestamp": "2025-08-28T03:53:43.826Z"}, {"module": "系统设置", "item": "重置设置按钮", "type": "button", "success": true, "message": "按钮点击成功，页面有响应", "details": {"urlChanged": false, "modalExists": false, "hasNewContent": false, "hasError": false, "beforeUrl": "http://localhost:4000/settings", "afterUrl": "http://localhost:4000/settings"}, "timestamp": "2025-08-28T03:53:47.138Z"}, {"module": "系统设置", "item": "备份数据按钮", "type": "button", "success": false, "message": "找到1个按钮，但不可见", "details": null, "timestamp": "2025-08-28T03:53:47.141Z"}, {"module": "系统设置", "item": "清除缓存按钮", "type": "button", "success": false, "message": "找到1个按钮，但不可见", "details": null, "timestamp": "2025-08-28T03:53:47.144Z"}], "screenshots": [{"name": "dashboard-initial", "filename": "fixed-screenshot-dashboard-initial-2025-08-28T03-52-12-502Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-dashboard-initial-2025-08-28T03-52-12-502Z.png", "timestamp": "2025-08-28T03-52-12-502Z"}, {"name": "dashboard-工具按钮-after-click", "filename": "fixed-screenshot-dashboard-工具按钮-after-click-2025-08-28T03-52-18-988Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-dashboard-工具按钮-after-click-2025-08-28T03-52-18-988Z.png", "timestamp": "2025-08-28T03-52-18-988Z"}, {"name": "dashboard-查看详情链接-after-click", "filename": "fixed-screenshot-dashboard-查看详情链接-after-click-2025-08-28T03-52-22-658Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-dashboard-查看详情链接-after-click-2025-08-28T03-52-22-658Z.png", "timestamp": "2025-08-28T03-52-22-658Z"}, {"name": "dashboard-卡片工具按钮-after-click", "filename": "fixed-screenshot-dashboard-卡片工具按钮-after-click-2025-08-28T03-52-27-577Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-dashboard-卡片工具按钮-after-click-2025-08-28T03-52-27-577Z.png", "timestamp": "2025-08-28T03-52-27-577Z"}, {"name": "tenants-initial", "filename": "fixed-screenshot-tenants-initial-2025-08-28T03-52-30-329Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-tenants-initial-2025-08-28T03-52-30-329Z.png", "timestamp": "2025-08-28T03-52-30-329Z"}, {"name": "tenants-主要操作按钮-after-click", "filename": "fixed-screenshot-tenants-主要操作按钮-after-click-2025-08-28T03-52-36-769Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-tenants-主要操作按钮-after-click-2025-08-28T03-52-36-769Z.png", "timestamp": "2025-08-28T03-52-36-769Z"}, {"name": "goosePrices-initial", "filename": "fixed-screenshot-goosePrices-initial-2025-08-28T03-52-41-152Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-goosePrices-initial-2025-08-28T03-52-41-152Z.png", "timestamp": "2025-08-28T03-52-41-152Z"}, {"name": "goosePrices-主要操作按钮-after-click", "filename": "fixed-screenshot-goosePrices-主要操作按钮-after-click-2025-08-28T03-52-47-688Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-goosePrices-主要操作按钮-after-click-2025-08-28T03-52-47-688Z.png", "timestamp": "2025-08-28T03-52-47-688Z"}, {"name": "announcements-initial", "filename": "fixed-screenshot-announcements-initial-2025-08-28T03-52-50-431Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-announcements-initial-2025-08-28T03-52-50-431Z.png", "timestamp": "2025-08-28T03-52-50-431Z"}, {"name": "announcements-创建公告按钮-after-click", "filename": "fixed-screenshot-announcements-创建公告按钮-after-click-2025-08-28T03-52-56-873Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-announcements-创建公告按钮-after-click-2025-08-28T03-52-56-873Z.png", "timestamp": "2025-08-28T03-52-56-873Z"}, {"name": "knowledge-initial", "filename": "fixed-screenshot-knowledge-initial-2025-08-28T03-52-59-612Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-knowledge-initial-2025-08-28T03-52-59-612Z.png", "timestamp": "2025-08-28T03-52-59-612Z"}, {"name": "knowledge-添加文章按钮-after-click", "filename": "fixed-screenshot-knowledge-添加文章按钮-after-click-2025-08-28T03-53-06-105Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-knowledge-添加文章按钮-after-click-2025-08-28T03-53-06-105Z.png", "timestamp": "2025-08-28T03-53-06-105Z"}, {"name": "mall-initial", "filename": "fixed-screenshot-mall-initial-2025-08-28T03-53-08-827Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-mall-initial-2025-08-28T03-53-08-827Z.png", "timestamp": "2025-08-28T03-53-08-827Z"}, {"name": "mall-添加商品按钮-after-click", "filename": "fixed-screenshot-mall-添加商品按钮-after-click-2025-08-28T03-53-15-385Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-mall-添加商品按钮-after-click-2025-08-28T03-53-15-385Z.png", "timestamp": "2025-08-28T03-53-15-385Z"}, {"name": "aiConfig-initial", "filename": "fixed-screenshot-aiConfig-initial-2025-08-28T03-53-18-126Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-aiConfig-initial-2025-08-28T03-53-18-126Z.png", "timestamp": "2025-08-28T03-53-18-126Z"}, {"name": "aiConfig-保存配置按钮-after-click", "filename": "fixed-screenshot-aiConfig-保存配置按钮-after-click-2025-08-28T03-53-24-539Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-aiConfig-保存配置按钮-after-click-2025-08-28T03-53-24-539Z.png", "timestamp": "2025-08-28T03-53-24-539Z"}, {"name": "aiConfig-测试连接按钮-after-click", "filename": "fixed-screenshot-aiConfig-测试连接按钮-after-click-2025-08-28T03-53-27-854Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-aiConfig-测试连接按钮-after-click-2025-08-28T03-53-27-854Z.png", "timestamp": "2025-08-28T03-53-27-854Z"}, {"name": "aiConfig-API测试按钮-after-click", "filename": "fixed-screenshot-aiConfig-API测试按钮-after-click-2025-08-28T03-53-31-161Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-aiConfig-API测试按钮-after-click-2025-08-28T03-53-31-161Z.png", "timestamp": "2025-08-28T03-53-31-161Z"}, {"name": "settings-initial", "filename": "fixed-screenshot-settings-initial-2025-08-28T03-53-34-020Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-settings-initial-2025-08-28T03-53-34-020Z.png", "timestamp": "2025-08-28T03-53-34-020Z"}, {"name": "settings-保存设置按钮-after-click", "filename": "fixed-screenshot-settings-保存设置按钮-after-click-2025-08-28T03-53-40-404Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-settings-保存设置按钮-after-click-2025-08-28T03-53-40-404Z.png", "timestamp": "2025-08-28T03-53-40-404Z"}, {"name": "settings-应用设置按钮-after-click", "filename": "fixed-screenshot-settings-应用设置按钮-after-click-2025-08-28T03-53-43-702Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-settings-应用设置按钮-after-click-2025-08-28T03-53-43-702Z.png", "timestamp": "2025-08-28T03-53-43-702Z"}, {"name": "settings-重置设置按钮-after-click", "filename": "fixed-screenshot-settings-重置设置按钮-after-click-2025-08-28T03-53-47-026Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/fixed-screenshot-settings-重置设置按钮-after-click-2025-08-28T03-53-47-026Z.png", "timestamp": "2025-08-28T03-53-47-026Z"}]}