/**
 * 修复SAAS管理后台数据库表结构脚本
 */

const path = require('path');
const crypto = require('crypto');

// 使用SAAS管理后台的数据库连接
const saasAdminPath = path.join(__dirname, '../backend/saas-admin');
const db = require(path.join(saasAdminPath, 'config/database'));

// 简单的密码哈希函数
function simpleHash(password) {
    return crypto.createHash('sha256').update(password + 'smartgoose_salt_2024').digest('hex');
}

async function fixDatabaseStructure() {
    try {
        console.log('🔧 开始修复SAAS管理后台数据库结构...');
        console.log('=' .repeat(60));
        
        // 测试数据库连接
        console.log('🔗 测试数据库连接...');
        await db.testConnection();
        console.log('✅ 数据库连接成功');

        // 检查并修复users表结构
        console.log('\n📋 检查users表结构...');
        
        // 获取当前表结构
        const result = await db.execute("SHOW COLUMNS FROM users");
        const columns = Array.isArray(result[0]) ? result[0] : [result[0]];
        const existingColumns = columns.map(col => col.Field);
        
        console.log('📊 当前表字段:', existingColumns);

        // 需要添加的字段
        const requiredColumns = [
            {
                name: 'username',
                definition: 'VARCHAR(50) UNIQUE NOT NULL',
                after: 'id'
            },
            {
                name: 'password',
                definition: 'VARCHAR(255) NOT NULL',
                after: 'username'
            },
            {
                name: 'name',
                definition: 'VARCHAR(100)',
                after: 'password'
            },
            {
                name: 'email',
                definition: 'VARCHAR(100) UNIQUE',
                after: 'name'
            },
            {
                name: 'role',
                definition: "ENUM('super_admin', 'platform_admin', 'tenant_admin', 'admin', 'user') DEFAULT 'user'",
                after: 'email'
            },
            {
                name: 'status',
                definition: "ENUM('active', 'inactive', 'suspended') DEFAULT 'active'",
                after: 'role'
            },
            {
                name: 'created_at',
                definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                after: 'status'
            },
            {
                name: 'updated_at',
                definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
                after: 'created_at'
            }
        ];

        // 添加缺失的字段
        let addedColumns = 0;
        for (const column of requiredColumns) {
            if (!existingColumns.includes(column.name)) {
                try {
                    const alterSQL = `ALTER TABLE users ADD COLUMN ${column.name} ${column.definition} AFTER ${column.after}`;
                    console.log(`🔧 添加字段: ${column.name}`);
                    await db.execute(alterSQL);
                    addedColumns++;
                } catch (error) {
                    console.log(`❌ 添加字段 ${column.name} 失败: ${error.message}`);
                }
            } else {
                console.log(`✅ 字段 ${column.name} 已存在`);
            }
        }

        if (addedColumns > 0) {
            console.log(`\n🎉 成功添加 ${addedColumns} 个字段到users表`);
        } else {
            console.log('\n✅ users表结构已完整');
        }

        // 创建默认管理员账号
        console.log('\n👤 创建默认管理员账号...');
        
        const defaultAdmins = [
            {
                username: 'super_admin',
                password: simpleHash('admin123'),
                name: '超级管理员',
                email: '<EMAIL>',
                role: 'super_admin'
            },
            {
                username: 'admin',
                password: simpleHash('admin123'),
                name: '系统管理员',
                email: '<EMAIL>',
                role: 'admin'
            }
        ];

        let createdAdmins = 0;
        for (const admin of defaultAdmins) {
            try {
                // 检查用户是否已存在
                const [existing] = await db.execute('SELECT id FROM users WHERE username = ?', [admin.username]);
                
                if (existing.length === 0) {
                    const insertSQL = `
                        INSERT INTO users (username, password, name, email, role, status, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
                    `;
                    
                    await db.execute(insertSQL, [
                        admin.username,
                        admin.password,
                        admin.name,
                        admin.email,
                        admin.role
                    ]);
                    
                    console.log(`✅ 创建管理员: ${admin.username} (${admin.role})`);
                    createdAdmins++;
                } else {
                    console.log(`⚠️  管理员 ${admin.username} 已存在`);
                }
            } catch (error) {
                console.log(`❌ 创建管理员 ${admin.username} 失败: ${error.message}`);
            }
        }

        // 验证修复结果
        console.log('\n🔍 验证修复结果...');

        const finalResult = await db.execute("SHOW COLUMNS FROM users");
        const finalColumns = Array.isArray(finalResult[0]) ? finalResult[0] : [finalResult[0]];
        console.log('📊 最终表结构:');
        finalColumns.forEach((col, index) => {
            console.log(`   ${index + 1}. ${col.Field} - ${col.Type} - ${col.Null} - ${col.Key}`);
        });

        const [userCount] = await db.execute('SELECT COUNT(*) as count FROM users');
        console.log(`\n👥 用户总数: ${userCount[0].count}`);

        const [adminCount] = await db.execute("SELECT COUNT(*) as count FROM users WHERE role IN ('super_admin', 'admin')");
        console.log(`👑 管理员数量: ${adminCount[0].count}`);

        console.log('\n🎯 数据库结构修复完成！');
        console.log('🔑 默认登录凭据:');
        console.log('   用户名: super_admin 或 admin');
        console.log('   密码: admin123');
        
        return true;

    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error.message);
        return false;
    }
}

// 主函数
async function main() {
    const success = await fixDatabaseStructure();
    
    if (success) {
        console.log('\n✅ 修复成功！现在可以进行完整的功能测试了');
        process.exit(0);
    } else {
        console.log('\n❌ 修复失败，请检查错误信息');
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = { fixDatabaseStructure };
