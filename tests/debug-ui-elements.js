/**
 * 调试UI元素可见性问题
 */

const { chromium } = require('playwright');

async function debugUIElements() {
  console.log('🔍 开始调试UI元素可见性问题...');
  
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  try {
    // 登录
    console.log('📝 登录系统...');
    await page.goto('http://localhost:4000/login');

    // 等待登录页面加载
    await page.waitForSelector('input[name="username"]', { timeout: 10000 });
    console.log('✅ 登录页面加载完成');

    await page.fill('input[name="username"]', 'super_admin');
    await page.fill('input[name="password"]', 'admin123');

    console.log('📝 提交登录表单...');
    await page.click('button[type="submit"]');

    // 等待登录结果
    try {
      await page.waitForURL('**/dashboard', { timeout: 10000 });
      console.log('✅ 登录成功，进入仪表盘');
    } catch (error) {
      console.log('⚠️  登录可能失败，检查当前URL...');
      const currentUrl = page.url();
      console.log('当前URL:', currentUrl);

      // 检查是否有错误消息
      const errorMsg = await page.locator('.alert-danger, .error-message').textContent().catch(() => null);
      if (errorMsg) {
        console.log('错误消息:', errorMsg);
      }

      // 如果还在登录页面，尝试直接访问仪表盘
      if (currentUrl.includes('/login')) {
        console.log('🔄 尝试直接访问仪表盘...');
        await page.goto('http://localhost:4000/dashboard');
        await page.waitForTimeout(2000);
      }
    }

    // 等待页面完全加载
    await page.waitForTimeout(2000);

    // 检查统计卡片
    console.log('\n📊 检查统计卡片...');
    const smallBoxes = await page.locator('.small-box').all();
    console.log(`找到 ${smallBoxes.length} 个 .small-box 元素`);
    
    for (let i = 0; i < smallBoxes.length; i++) {
      const box = smallBoxes[i];
      const isVisible = await box.isVisible();
      const text = await box.textContent();
      console.log(`  统计卡片 ${i + 1}: ${isVisible ? '✅ 可见' : '❌ 不可见'} - ${text?.substring(0, 50)}...`);
    }

    // 检查信息框
    console.log('\n📋 检查信息框...');
    const infoBoxes = await page.locator('.info-box').all();
    console.log(`找到 ${infoBoxes.length} 个 .info-box 元素`);
    
    for (let i = 0; i < Math.min(infoBoxes.length, 5); i++) {
      const box = infoBoxes[i];
      const isVisible = await box.isVisible();
      const text = await box.textContent();
      console.log(`  信息框 ${i + 1}: ${isVisible ? '✅ 可见' : '❌ 不可见'} - ${text?.substring(0, 30)}...`);
    }

    // 检查卡片容器
    console.log('\n🃏 检查卡片容器...');
    const cards = await page.locator('.card').all();
    console.log(`找到 ${cards.length} 个 .card 元素`);
    
    for (let i = 0; i < Math.min(cards.length, 3); i++) {
      const card = cards[i];
      const isVisible = await card.isVisible();
      const header = await card.locator('.card-header').textContent().catch(() => '无标题');
      console.log(`  卡片 ${i + 1}: ${isVisible ? '✅ 可见' : '❌ 不可见'} - ${header}`);
    }

    // 检查数据表格
    console.log('\n📊 检查数据表格...');
    const tables = await page.locator('.table').all();
    console.log(`找到 ${tables.length} 个 .table 元素`);
    
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      const isVisible = await table.isVisible();
      const rows = await table.locator('tbody tr').count();
      console.log(`  表格 ${i + 1}: ${isVisible ? '✅ 可见' : '❌ 不可见'} - ${rows} 行数据`);
    }

    // 检查按钮
    console.log('\n🔘 检查工具按钮...');
    const toolButtons = await page.locator('.card-tools .btn').all();
    console.log(`找到 ${toolButtons.length} 个 .card-tools .btn 元素`);
    
    for (let i = 0; i < toolButtons.length; i++) {
      const btn = toolButtons[i];
      const isVisible = await btn.isVisible();
      const text = await btn.textContent();
      console.log(`  工具按钮 ${i + 1}: ${isVisible ? '✅ 可见' : '❌ 不可见'} - ${text?.trim()}`);
    }

    // 截图保存
    await page.screenshot({ path: 'tests/debug-dashboard.png', fullPage: true });
    console.log('\n📸 已保存调试截图: tests/debug-dashboard.png');

    // 检查CSS样式
    console.log('\n🎨 检查CSS样式...');
    const firstSmallBox = page.locator('.small-box').first();
    if (await firstSmallBox.count() > 0) {
      const styles = await firstSmallBox.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          display: computed.display,
          visibility: computed.visibility,
          opacity: computed.opacity,
          position: computed.position,
          zIndex: computed.zIndex
        };
      });
      console.log('  第一个统计卡片样式:', styles);
    }

    // 检查JavaScript错误
    console.log('\n🐛 检查JavaScript错误...');
    const errors = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.waitForTimeout(1000);
    
    if (errors.length > 0) {
      console.log('  发现JavaScript错误:');
      errors.forEach((error, i) => {
        console.log(`    ${i + 1}. ${error}`);
      });
    } else {
      console.log('  ✅ 没有发现JavaScript错误');
    }

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

// 运行调试
if (require.main === module) {
  debugUIElements().then(() => {
    console.log('\n🎉 调试完成！');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 调试失败:', error);
    process.exit(1);
  });
}

module.exports = debugUIElements;
