/**
 * 智慧养鹅SAAS管理后台 - 彻底清理语法错误脚本
 * 目标：彻底清理所有错误的catch块和语法问题
 */

const fs = require('fs');
const path = require('path');

class SyntaxCleaner {
  constructor() {
    this.routeFile = path.join(__dirname, '../backend/saas-admin/routes/unified-saas-admin.js');
  }

  // 读取路由文件
  readRouteFile() {
    try {
      return fs.readFileSync(this.routeFile, 'utf8');
    } catch (error) {
      console.error('读取文件失败:', error.message);
      return null;
    }
  }

  // 写入路由文件
  writeRouteFile(content) {
    try {
      // 备份原文件
      const backupFile = this.routeFile + '.clean-backup.' + Date.now();
      fs.copyFileSync(this.routeFile, backupFile);
      console.log('✅ 备份文件:', backupFile);
      
      // 写入修复后的内容
      fs.writeFileSync(this.routeFile, content, 'utf8');
      console.log('✅ 写入清理后的文件');
      return true;
    } catch (error) {
      console.error('写入文件失败:', error.message);
      return false;
    }
  }

  // 彻底清理错误的catch块
  cleanupBadCatchBlocks(content) {
    let cleanCount = 0;
    
    // 移除所有孤立的catch块（不属于任何try块的catch）
    const badCatchPatterns = [
      // 模式1: 在简单路由后面的错误catch块
      /(\s+\}\);\s*)\s*\} catch \(error\) \{[\s\S]*?\}\s*\}\);/g,
      
      // 模式2: 在render后面的错误catch块
      /(\s+\}\);\s*)\s*\} catch \(error\) \{[\s\S]*?\}\s*\}\);/g,
      
      // 模式3: 单独的catch块
      /\s*\} catch \(error\) \{[\s\S]*?console\.error\([^)]*\);[\s\S]*?\}\s*\}\);/g
    ];

    badCatchPatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, '$1');
        cleanCount += matches.length;
        console.log(`✅ 清理模式 ${index + 1}: 移除了 ${matches.length} 个错误的catch块`);
      }
    });

    // 特殊处理：移除租户级路由中的错误catch块
    const tenantRoutePattern = /(router\.get\('\/tenant\/\w+',[\s\S]*?res\.render\([^)]*\);[\s\S]*?\}\);)\s*\} catch \(error\) \{[\s\S]*?\}\s*\}\);/g;
    content = content.replace(tenantRoutePattern, '$1');

    console.log(`✅ 总共清理了 ${cleanCount} 个错误的catch块`);
    return content;
  }

  // 修复不完整的路由
  fixIncompleteRoutes(content) {
    let fixCount = 0;

    // 确保所有async路由都有try-catch结构
    const asyncRoutePattern = /(router\.\w+\([^,]+,[\s\S]*?async \(req, res\) => \{)\s*([\s\S]*?)(res\.render\([^)]*\);)\s*(\}\);)/g;
    
    content = content.replace(asyncRoutePattern, (match, routeStart, routeBody, renderCall, routeEnd) => {
      // 检查是否已经有try-catch结构
      if (routeBody.includes('try {') && routeBody.includes('} catch')) {
        return match; // 已经有完整的try-catch，不需要修改
      }
      
      // 添加try-catch结构
      fixCount++;
      return `${routeStart}
  try {
    ${routeBody.trim()}
    ${renderCall}
  } catch (error) {
    console.error('页面加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '页面加载失败，请重试',
      layout: 'layouts/main'
    });
  }
${routeEnd}`;
    });

    if (fixCount > 0) {
      console.log(`✅ 修复了 ${fixCount} 个不完整的async路由`);
    }

    return content;
  }

  // 清理多余的空行和格式问题
  cleanupFormatting(content) {
    // 移除多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // 修复缩进问题
    content = content.replace(/^[ \t]+$/gm, '');
    
    // 确保文件以换行符结尾
    if (!content.endsWith('\n')) {
      content += '\n';
    }
    
    console.log('✅ 清理了格式问题');
    return content;
  }

  // 验证语法
  validateSyntax(content) {
    try {
      const openBraces = (content.match(/\{/g) || []).length;
      const closeBraces = (content.match(/\}/g) || []).length;
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      
      console.log(`语法检查: 大括号 ${openBraces}/${closeBraces}, 小括号 ${openParens}/${closeParens}`);
      
      if (openBraces !== closeBraces) {
        console.error(`❌ 大括号不匹配: 开启${openBraces}个，关闭${closeBraces}个`);
        return false;
      }
      
      if (openParens !== closeParens) {
        console.error(`❌ 小括号不匹配: 开启${openParens}个，关闭${closeParens}个`);
        return false;
      }
      
      // 检查是否还有孤立的catch块
      const badCatchPattern = /\}\);\s*\} catch/;
      if (badCatchPattern.test(content)) {
        console.error('❌ 仍然存在孤立的catch块');
        return false;
      }
      
      console.log('✅ 语法检查通过');
      return true;
    } catch (error) {
      console.error('❌ 语法验证失败:', error.message);
      return false;
    }
  }

  // 执行彻底清理
  async performCleanup() {
    console.log('🧹 开始彻底清理语法错误...');
    console.log('=' .repeat(60));

    // 读取文件
    let content = this.readRouteFile();
    if (!content) {
      return false;
    }

    console.log(`原始文件行数: ${content.split('\n').length}`);

    // 执行清理
    content = this.cleanupBadCatchBlocks(content);
    content = this.fixIncompleteRoutes(content);
    content = this.cleanupFormatting(content);

    console.log(`清理后文件行数: ${content.split('\n').length}`);

    // 验证语法
    const isValid = this.validateSyntax(content);
    
    // 写入文件
    const success = this.writeRouteFile(content);
    
    if (success) {
      console.log('\n🎉 彻底清理完成！');
      console.log(`语法验证: ${isValid ? '✅ 通过' : '❌ 失败'}`);
      return isValid;
    } else {
      console.log('\n❌ 清理失败');
      return false;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const cleaner = new SyntaxCleaner();
  cleaner.performCleanup().then(success => {
    console.log(success ? '\n✅ 彻底清理成功！' : '\n❌ 清理失败！');
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ 清理过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = SyntaxCleaner;
