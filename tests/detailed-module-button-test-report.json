{"summary": {"totalTests": 71, "passedTests": 8, "failedTests": 63, "successRate": "11.27%", "testDate": "2025-08-28T02:05:08.253Z", "testDuration": "N/A"}, "moduleStats": {"平台仪表盘": {"total": 8, "passed": 1, "failed": 7}, "租户管理": {"total": 9, "passed": 1, "failed": 8}, "今日鹅价管理": {"total": 9, "passed": 1, "failed": 8}, "平台公告管理": {"total": 9, "passed": 1, "failed": 8}, "知识库管理": {"total": 9, "passed": 1, "failed": 8}, "商城模块管理": {"total": 9, "passed": 1, "failed": 8}, "AI大模型配置": {"total": 9, "passed": 1, "failed": 8}, "系统设置": {"total": 9, "passed": 1, "failed": 8}}, "buttonStats": {"平台仪表盘": {"total": 4, "passed": 0, "failed": 4}, "租户管理": {"total": 5, "passed": 0, "failed": 5}, "今日鹅价管理": {"total": 5, "passed": 0, "failed": 5}, "平台公告管理": {"total": 5, "passed": 0, "failed": 5}, "知识库管理": {"total": 5, "passed": 0, "failed": 5}, "商城模块管理": {"total": 5, "passed": 0, "failed": 5}, "AI大模型配置": {"total": 5, "passed": 0, "failed": 5}, "系统设置": {"total": 5, "passed": 0, "failed": 5}}, "elementStats": {"平台仪表盘": {"total": 3, "passed": 0, "failed": 3}, "租户管理": {"total": 3, "passed": 0, "failed": 3}, "今日鹅价管理": {"total": 3, "passed": 0, "failed": 3}, "平台公告管理": {"total": 3, "passed": 0, "failed": 3}, "知识库管理": {"total": 3, "passed": 0, "failed": 3}, "商城模块管理": {"total": 3, "passed": 0, "failed": 3}, "AI大模型配置": {"total": 3, "passed": 0, "failed": 3}, "系统设置": {"total": 3, "passed": 0, "failed": 3}}, "detailedResults": [{"module": "平台仪表盘", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:30.480Z"}, {"module": "平台仪表盘", "item": "统计卡片", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:32.509Z"}, {"module": "平台仪表盘", "item": "图表容器", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:32.512Z"}, {"module": "平台仪表盘", "item": "数据表格", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:32.515Z"}, {"module": "平台仪表盘", "item": "刷新数据", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.517Z"}, {"module": "平台仪表盘", "item": "导出报表", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.519Z"}, {"module": "平台仪表盘", "item": "日期选择器", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.523Z"}, {"module": "平台仪表盘", "item": "筛选按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.524Z"}, {"module": "租户管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:35.442Z"}, {"module": "租户管理", "item": "租户列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:37.462Z"}, {"module": "租户管理", "item": "搜索框", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:37.463Z"}, {"module": "租户管理", "item": "分页组件", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:37.464Z"}, {"module": "租户管理", "item": "添加租户", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.465Z"}, {"module": "租户管理", "item": "编辑租户", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.466Z"}, {"module": "租户管理", "item": "删除租户", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.467Z"}, {"module": "租户管理", "item": "搜索按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.468Z"}, {"module": "租户管理", "item": "导出列表", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.469Z"}, {"module": "今日鹅价管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:40.195Z"}, {"module": "今日鹅价管理", "item": "价格列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:42.222Z"}, {"module": "今日鹅价管理", "item": "价格表单", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.224Z"}, {"module": "今日鹅价管理", "item": "价格走势图", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.225Z"}, {"module": "今日鹅价管理", "item": "添加价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.225Z"}, {"module": "今日鹅价管理", "item": "编辑价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.228Z"}, {"module": "今日鹅价管理", "item": "删除价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.229Z"}, {"module": "今日鹅价管理", "item": "发布价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.229Z"}, {"module": "今日鹅价管理", "item": "价格历史", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.230Z"}, {"module": "平台公告管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:44.956Z"}, {"module": "平台公告管理", "item": "公告列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:46.983Z"}, {"module": "平台公告管理", "item": "编辑器容器", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.984Z"}, {"module": "平台公告管理", "item": "状态筛选", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:46.985Z"}, {"module": "平台公告管理", "item": "创建公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.987Z"}, {"module": "平台公告管理", "item": "编辑公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.987Z"}, {"module": "平台公告管理", "item": "删除公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.989Z"}, {"module": "平台公告管理", "item": "发布公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.990Z"}, {"module": "平台公告管理", "item": "预览公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.991Z"}, {"module": "知识库管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:49.729Z"}, {"module": "知识库管理", "item": "知识树", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:51.758Z"}, {"module": "知识库管理", "item": "文章列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:51.759Z"}, {"module": "知识库管理", "item": "分类侧边栏", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:51.760Z"}, {"module": "知识库管理", "item": "添加文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.762Z"}, {"module": "知识库管理", "item": "编辑文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.764Z"}, {"module": "知识库管理", "item": "删除文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.766Z"}, {"module": "知识库管理", "item": "分类管理", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.766Z"}, {"module": "知识库管理", "item": "搜索文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.767Z"}, {"module": "商城模块管理", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:54.531Z"}, {"module": "商城模块管理", "item": "商品网格", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:56.554Z"}, {"module": "商城模块管理", "item": "分类导航", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:56.557Z"}, {"module": "商城模块管理", "item": "订单概览", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.558Z"}, {"module": "商城模块管理", "item": "添加商品", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.558Z"}, {"module": "商城模块管理", "item": "编辑商品", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.559Z"}, {"module": "商城模块管理", "item": "删除商品", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.559Z"}, {"module": "商城模块管理", "item": "分类管理", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.560Z"}, {"module": "商城模块管理", "item": "订单管理", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.561Z"}, {"module": "AI大模型配置", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:04:59.319Z"}, {"module": "AI大模型配置", "item": "配置表单", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:01.339Z"}, {"module": "AI大模型配置", "item": "模型列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:01.341Z"}, {"module": "AI大模型配置", "item": "测试结果", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.341Z"}, {"module": "AI大模型配置", "item": "保存配置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.342Z"}, {"module": "AI大模型配置", "item": "测试连接", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.343Z"}, {"module": "AI大模型配置", "item": "重置配置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.343Z"}, {"module": "AI大模型配置", "item": "模型选择", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.344Z"}, {"module": "AI大模型配置", "item": "API测试", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.344Z"}, {"module": "系统设置", "item": "页面导航", "type": "navigation", "success": true, "message": "成功导航到模块页面", "details": null, "timestamp": "2025-08-28T02:05:04.219Z"}, {"module": "系统设置", "item": "设置标签页", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:06.240Z"}, {"module": "系统设置", "item": "设置表单", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:06.241Z"}, {"module": "系统设置", "item": "系统信息", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:06.243Z"}, {"module": "系统设置", "item": "保存设置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.244Z"}, {"module": "系统设置", "item": "重置设置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.245Z"}, {"module": "系统设置", "item": "备份数据", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.246Z"}, {"module": "系统设置", "item": "恢复数据", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.247Z"}, {"module": "系统设置", "item": "清除缓存", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.248Z"}], "screenshots": [{"name": "dashboard-initial", "filename": "screenshot-dashboard-initial-2025-08-28T02-04-30-290Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-dashboard-initial-2025-08-28T02-04-30-290Z.png", "timestamp": "2025-08-28T02-04-30-290Z"}, {"name": "tenants-initial", "filename": "screenshot-tenants-initial-2025-08-28T02-04-35-264Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-tenants-initial-2025-08-28T02-04-35-264Z.png", "timestamp": "2025-08-28T02-04-35-264Z"}, {"name": "goosePrices-initial", "filename": "screenshot-goosePrices-initial-2025-08-28T02-04-40-082Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-goosePrices-initial-2025-08-28T02-04-40-082Z.png", "timestamp": "2025-08-28T02-04-40-082Z"}, {"name": "announcements-initial", "filename": "screenshot-announcements-initial-2025-08-28T02-04-44-857Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-announcements-initial-2025-08-28T02-04-44-857Z.png", "timestamp": "2025-08-28T02-04-44-857Z"}, {"name": "knowledge-initial", "filename": "screenshot-knowledge-initial-2025-08-28T02-04-49-627Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-knowledge-initial-2025-08-28T02-04-49-627Z.png", "timestamp": "2025-08-28T02-04-49-627Z"}, {"name": "mall-initial", "filename": "screenshot-mall-initial-2025-08-28T02-04-54-440Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-mall-initial-2025-08-28T02-04-54-440Z.png", "timestamp": "2025-08-28T02-04-54-440Z"}, {"name": "aiConfig-initial", "filename": "screenshot-aiConfig-initial-2025-08-28T02-04-59-211Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-aiConfig-initial-2025-08-28T02-04-59-211Z.png", "timestamp": "2025-08-28T02-04-59-211Z"}, {"name": "settings-initial", "filename": "screenshot-settings-initial-2025-08-28T02-05-04-039Z.png", "filepath": "/Volumes/DATA/千问/智慧养鹅全栈/tests/screenshots/screenshot-settings-initial-2025-08-28T02-05-04-039Z.png", "timestamp": "2025-08-28T02-05-04-039Z"}], "failedTests": [{"module": "平台仪表盘", "item": "统计卡片", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:32.509Z"}, {"module": "平台仪表盘", "item": "图表容器", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:32.512Z"}, {"module": "平台仪表盘", "item": "数据表格", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:32.515Z"}, {"module": "平台仪表盘", "item": "刷新数据", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.517Z"}, {"module": "平台仪表盘", "item": "导出报表", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.519Z"}, {"module": "平台仪表盘", "item": "日期选择器", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.523Z"}, {"module": "平台仪表盘", "item": "筛选按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:32.524Z"}, {"module": "租户管理", "item": "租户列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:37.462Z"}, {"module": "租户管理", "item": "搜索框", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:37.463Z"}, {"module": "租户管理", "item": "分页组件", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:37.464Z"}, {"module": "租户管理", "item": "添加租户", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.465Z"}, {"module": "租户管理", "item": "编辑租户", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.466Z"}, {"module": "租户管理", "item": "删除租户", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.467Z"}, {"module": "租户管理", "item": "搜索按钮", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.468Z"}, {"module": "租户管理", "item": "导出列表", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:37.469Z"}, {"module": "今日鹅价管理", "item": "价格列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:42.222Z"}, {"module": "今日鹅价管理", "item": "价格表单", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.224Z"}, {"module": "今日鹅价管理", "item": "价格走势图", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.225Z"}, {"module": "今日鹅价管理", "item": "添加价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.225Z"}, {"module": "今日鹅价管理", "item": "编辑价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.228Z"}, {"module": "今日鹅价管理", "item": "删除价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.229Z"}, {"module": "今日鹅价管理", "item": "发布价格", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.229Z"}, {"module": "今日鹅价管理", "item": "价格历史", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:42.230Z"}, {"module": "平台公告管理", "item": "公告列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:46.983Z"}, {"module": "平台公告管理", "item": "编辑器容器", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.984Z"}, {"module": "平台公告管理", "item": "状态筛选", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:46.985Z"}, {"module": "平台公告管理", "item": "创建公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.987Z"}, {"module": "平台公告管理", "item": "编辑公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.987Z"}, {"module": "平台公告管理", "item": "删除公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.989Z"}, {"module": "平台公告管理", "item": "发布公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.990Z"}, {"module": "平台公告管理", "item": "预览公告", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:46.991Z"}, {"module": "知识库管理", "item": "知识树", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:51.758Z"}, {"module": "知识库管理", "item": "文章列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:51.759Z"}, {"module": "知识库管理", "item": "分类侧边栏", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:51.760Z"}, {"module": "知识库管理", "item": "添加文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.762Z"}, {"module": "知识库管理", "item": "编辑文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.764Z"}, {"module": "知识库管理", "item": "删除文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.766Z"}, {"module": "知识库管理", "item": "分类管理", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.766Z"}, {"module": "知识库管理", "item": "搜索文章", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:51.767Z"}, {"module": "商城模块管理", "item": "商品网格", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:56.554Z"}, {"module": "商城模块管理", "item": "分类导航", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:04:56.557Z"}, {"module": "商城模块管理", "item": "订单概览", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.558Z"}, {"module": "商城模块管理", "item": "添加商品", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.558Z"}, {"module": "商城模块管理", "item": "编辑商品", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.559Z"}, {"module": "商城模块管理", "item": "删除商品", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.559Z"}, {"module": "商城模块管理", "item": "分类管理", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.560Z"}, {"module": "商城模块管理", "item": "订单管理", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:04:56.561Z"}, {"module": "AI大模型配置", "item": "配置表单", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:01.339Z"}, {"module": "AI大模型配置", "item": "模型列表", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:01.341Z"}, {"module": "AI大模型配置", "item": "测试结果", "type": "element", "success": false, "message": "元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.341Z"}, {"module": "AI大模型配置", "item": "保存配置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.342Z"}, {"module": "AI大模型配置", "item": "测试连接", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.343Z"}, {"module": "AI大模型配置", "item": "重置配置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.343Z"}, {"module": "AI大模型配置", "item": "模型选择", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.344Z"}, {"module": "AI大模型配置", "item": "API测试", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:01.344Z"}, {"module": "系统设置", "item": "设置标签页", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:06.240Z"}, {"module": "系统设置", "item": "设置表单", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:06.241Z"}, {"module": "系统设置", "item": "系统信息", "type": "element", "success": null, "message": "元素不可见或不存在", "details": null, "timestamp": "2025-08-28T02:05:06.243Z"}, {"module": "系统设置", "item": "保存设置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.244Z"}, {"module": "系统设置", "item": "重置设置", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.245Z"}, {"module": "系统设置", "item": "备份数据", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.246Z"}, {"module": "系统设置", "item": "恢复数据", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.247Z"}, {"module": "系统设置", "item": "清除缓存", "type": "button", "success": false, "message": "按钮元素不存在", "details": null, "timestamp": "2025-08-28T02:05:06.248Z"}], "recommendations": [{"type": "UI元素缺失", "severity": "high", "description": "多个模块中存在UI元素缺失问题", "affectedModules": ["平台仪表盘", "租户管理", "今日鹅价管理", "平台公告管理", "知识库管理", "商城模块管理", "AI大模型配置", "系统设置"], "suggestion": "检查前端页面模板，确保所有必要的UI元素都已正确渲染"}, {"type": "UI元素缺失", "severity": "high", "description": "多个模块中存在UI元素缺失问题", "affectedModules": ["今日鹅价管理", "平台公告管理", "商城模块管理", "AI大模型配置"], "suggestion": "检查前端页面模板，确保所有必要的UI元素都已正确渲染"}]}