/**
 * 检查括号匹配的简单脚本
 */

const fs = require('fs');
const path = require('path');

const routeFile = path.join(__dirname, '../backend/saas-admin/routes/unified-saas-admin.js');
const content = fs.readFileSync(routeFile, 'utf8');

let braceStack = [];
let parenStack = [];
let bracketStack = [];

const lines = content.split('\n');

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  const lineNum = i + 1;
  
  // 跳过注释和字符串内容
  let inString = false;
  let inComment = false;
  let stringChar = '';
  
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    const prevChar = j > 0 ? line[j-1] : '';
    
    // 处理字符串
    if (!inComment && (char === '"' || char === "'" || char === '`')) {
      if (!inString) {
        inString = true;
        stringChar = char;
      } else if (char === stringChar && prevChar !== '\\') {
        inString = false;
        stringChar = '';
      }
      continue;
    }
    
    // 处理注释
    if (!inString && char === '/' && j < line.length - 1 && line[j+1] === '/') {
      inComment = true;
      break;
    }
    
    if (inString || inComment) continue;
    
    // 处理括号
    switch (char) {
      case '{':
        braceStack.push({ line: lineNum, col: j + 1 });
        break;
      case '}':
        if (braceStack.length === 0) {
          console.log(`❌ 第${lineNum}行第${j+1}列: 多余的 }`);
        } else {
          braceStack.pop();
        }
        break;
      case '(':
        parenStack.push({ line: lineNum, col: j + 1 });
        break;
      case ')':
        if (parenStack.length === 0) {
          console.log(`❌ 第${lineNum}行第${j+1}列: 多余的 )`);
        } else {
          parenStack.pop();
        }
        break;
      case '[':
        bracketStack.push({ line: lineNum, col: j + 1 });
        break;
      case ']':
        if (bracketStack.length === 0) {
          console.log(`❌ 第${lineNum}行第${j+1}列: 多余的 ]`);
        } else {
          bracketStack.pop();
        }
        break;
    }
  }
}

console.log('括号匹配检查结果:');
console.log(`大括号: ${braceStack.length === 0 ? '✅ 匹配' : `❌ 缺少${braceStack.length}个 }`}`);
console.log(`小括号: ${parenStack.length === 0 ? '✅ 匹配' : `❌ 缺少${parenStack.length}个 )`}`);
console.log(`方括号: ${bracketStack.length === 0 ? '✅ 匹配' : `❌ 缺少${bracketStack.length}个 ]`}`);

if (braceStack.length > 0) {
  console.log('\n未闭合的大括号位置:');
  braceStack.forEach(pos => {
    console.log(`  第${pos.line}行第${pos.col}列`);
  });
}

if (parenStack.length > 0) {
  console.log('\n未闭合的小括号位置:');
  parenStack.forEach(pos => {
    console.log(`  第${pos.line}行第${pos.col}列`);
  });
}

if (bracketStack.length > 0) {
  console.log('\n未闭合的方括号位置:');
  bracketStack.forEach(pos => {
    console.log(`  第${pos.line}行第${pos.col}列`);
  });
}
