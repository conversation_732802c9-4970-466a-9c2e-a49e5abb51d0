/**
 * 创建SAAS管理后台管理员账号脚本
 */

// 使用SAAS管理后台的数据库连接
const path = require('path');
const saasAdminPath = path.join(__dirname, '../backend/saas-admin');
const db = require(path.join(saasAdminPath, 'config/database'));
const bcrypt = require('bcrypt');

async function createAdminUser() {
    try {
        console.log('🔗 连接数据库...');
        await db.testConnection();
        console.log('✅ 数据库连接成功');

        // 检查users表是否存在
        console.log('\n📋 检查数据库表结构...');
        const [tables] = await db.execute("SHOW TABLES LIKE 'users'");

        if (tables.length === 0) {
            console.log('❌ users表不存在，需要先运行数据库迁移脚本');
            return false;
        }

        // 检查现有用户
        console.log('\n👥 检查现有用户...');
        const [existingUsers] = await db.execute('SELECT id, username, role FROM users LIMIT 10');
        
        if (existingUsers.length > 0) {
            console.log('📊 现有用户列表:');
            existingUsers.forEach(user => {
                console.log(`   - ID: ${user.id}, 用户名: ${user.username}, 角色: ${user.role}`);
            });
        } else {
            console.log('📊 数据库中暂无用户');
        }

        // 检查是否已有管理员账号
        const [adminUsers] = await db.execute(
            "SELECT * FROM users WHERE role IN ('super_admin', 'platform_admin', 'admin') LIMIT 1"
        );

        if (adminUsers.length > 0) {
            console.log(`\n✅ 已存在管理员账号: ${adminUsers[0].username}`);
            console.log('🔑 可以使用以下凭据登录:');
            console.log(`   用户名: ${adminUsers[0].username}`);
            console.log(`   密码: admin123 (如果是默认密码)`);
            return true;
        }

        // 创建管理员账号
        console.log('\n🔧 创建管理员账号...');

        const adminData = {
            username: 'super_admin',
            password: await bcrypt.hash('admin123', 10),
            name: '超级管理员',
            email: '<EMAIL>',
            role: 'super_admin',
            status: 'active'
        };

        const insertQuery = `
            INSERT INTO users (username, password, name, email, role, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;

        const [result] = await db.execute(insertQuery, [
            adminData.username,
            adminData.password,
            adminData.name,
            adminData.email,
            adminData.role,
            adminData.status
        ]);

        if (result.insertId) {
            console.log('✅ 管理员账号创建成功!');
            console.log('🔑 登录凭据:');
            console.log(`   用户名: ${adminData.username}`);
            console.log(`   密码: admin123`);
            console.log(`   角色: ${adminData.role}`);
            return true;
        } else {
            console.log('❌ 管理员账号创建失败');
            return false;
        }

    } catch (error) {
        console.error('❌ 操作失败:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 建议: 请确保MySQL服务正在运行');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 建议: 请检查数据库连接凭据');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('💡 建议: 请确保数据库 smart_goose_saas_platform 存在');
        }
        
        return false;
    }
}

// 检查数据库连接
async function testDatabaseConnection() {
    try {
        console.log('🧪 测试数据库连接...');
        await db.testConnection();
        console.log('✅ 数据库连接测试成功');
        return true;
    } catch (error) {
        console.error('❌ 数据库连接测试失败:', error.message);
        return false;
    }
}

// 主函数
async function main() {
    console.log('🚀 SAAS管理后台管理员账号创建工具');
    console.log('=' .repeat(50));

    // 测试数据库连接
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
        console.log('\n❌ 无法连接到数据库，请检查配置');
        process.exit(1);
    }

    // 创建管理员账号
    const success = await createAdminUser();
    
    if (success) {
        console.log('\n🎉 操作完成！现在可以使用管理员账号登录系统了');
        process.exit(0);
    } else {
        console.log('\n❌ 操作失败，请检查错误信息');
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = { createAdminUser, testDatabaseConnection };
