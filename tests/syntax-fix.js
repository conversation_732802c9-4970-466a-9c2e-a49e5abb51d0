/**
 * 智慧养鹅SAAS管理后台 - 语法错误修复脚本
 * 目标：修复所有重复的catch块和语法错误
 */

const fs = require('fs');
const path = require('path');

class SyntaxFixer {
  constructor() {
    this.routeFile = path.join(__dirname, '../backend/saas-admin/routes/unified-saas-admin.js');
    this.fixes = [];
  }

  // 记录修复操作
  logFix(operation, description, success = true) {
    const fix = {
      operation,
      description,
      success,
      timestamp: new Date().toISOString()
    };
    this.fixes.push(fix);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${operation}: ${description}`);
  }

  // 读取路由文件
  readRouteFile() {
    try {
      const content = fs.readFileSync(this.routeFile, 'utf8');
      this.logFix('读取文件', '成功读取路由文件');
      return content;
    } catch (error) {
      this.logFix('读取文件', `读取失败: ${error.message}`, false);
      return null;
    }
  }

  // 写入路由文件
  writeRouteFile(content) {
    try {
      // 备份原文件
      const backupFile = this.routeFile + '.syntax-backup.' + Date.now();
      fs.copyFileSync(this.routeFile, backupFile);
      this.logFix('备份文件', `备份到: ${backupFile}`);
      
      // 写入修复后的内容
      fs.writeFileSync(this.routeFile, content, 'utf8');
      this.logFix('写入文件', '成功写入修复后的路由文件');
      return true;
    } catch (error) {
      this.logFix('写入文件', `写入失败: ${error.message}`, false);
      return false;
    }
  }

  // 修复重复的catch块
  fixDuplicateCatchBlocks(content) {
    let fixCount = 0;
    
    // 修复模式1: });  } catch (error) { ... }
    const pattern1 = /\}\);\s*\} catch \(error\) \{[\s\S]*?\}\s*\}\);/g;
    content = content.replace(pattern1, (match) => {
      fixCount++;
      return '});';
    });
    
    // 修复模式2: }  } catch (error) { ... }
    const pattern2 = /\}\s*\} catch \(error\) \{[\s\S]*?\}\s*\}\);/g;
    content = content.replace(pattern2, (match) => {
      fixCount++;
      return '  }\n});';
    });
    
    // 修复模式3: 单独的多余catch块
    const pattern3 = /\s*\} catch \(error\) \{[\s\S]*?console\.error\([^)]*\);[\s\S]*?\}\s*\}\);/g;
    content = content.replace(pattern3, (match) => {
      // 检查前面是否已经有完整的try-catch结构
      if (match.includes('});')) {
        fixCount++;
        return '\n});';
      }
      return match;
    });

    if (fixCount > 0) {
      this.logFix('修复语法', `修复了${fixCount}个重复的catch块`);
    }
    
    return content;
  }

  // 修复不匹配的括号
  fixUnmatchedBraces(content) {
    const lines = content.split('\n');
    let braceStack = [];
    let parenStack = [];
    let fixCount = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 跳过注释和字符串
      if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
        continue;
      }
      
      // 计算括号
      for (let char of line) {
        if (char === '{') {
          braceStack.push(i);
        } else if (char === '}') {
          if (braceStack.length > 0) {
            braceStack.pop();
          }
        } else if (char === '(') {
          parenStack.push(i);
        } else if (char === ')') {
          if (parenStack.length > 0) {
            parenStack.pop();
          }
        }
      }
    }
    
    if (fixCount > 0) {
      this.logFix('修复括号', `修复了${fixCount}个不匹配的括号`);
    }
    
    return content;
  }

  // 清理多余的空行和格式问题
  cleanupFormatting(content) {
    // 移除多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // 修复缩进问题
    content = content.replace(/^[ \t]+$/gm, '');
    
    // 确保文件以换行符结尾
    if (!content.endsWith('\n')) {
      content += '\n';
    }
    
    this.logFix('清理格式', '清理了多余的空行和格式问题');
    return content;
  }

  // 验证语法
  validateSyntax(content) {
    try {
      // 简单的语法检查
      const openBraces = (content.match(/\{/g) || []).length;
      const closeBraces = (content.match(/\}/g) || []).length;
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      
      if (openBraces !== closeBraces) {
        this.logFix('语法验证', `大括号不匹配: 开启${openBraces}个，关闭${closeBraces}个`, false);
        return false;
      }
      
      if (openParens !== closeParens) {
        this.logFix('语法验证', `小括号不匹配: 开启${openParens}个，关闭${closeParens}个`, false);
        return false;
      }
      
      // 检查常见的语法错误模式
      const duplicateCatch = /\}\);\s*\} catch/g;
      if (duplicateCatch.test(content)) {
        this.logFix('语法验证', '仍然存在重复的catch块', false);
        return false;
      }
      
      this.logFix('语法验证', '语法检查通过');
      return true;
    } catch (error) {
      this.logFix('语法验证', `验证失败: ${error.message}`, false);
      return false;
    }
  }

  // 执行所有语法修复
  async fixAllSyntaxErrors() {
    console.log('🔧 开始修复SAAS管理后台语法错误...');
    console.log('🎯 目标：修复所有语法错误，确保服务器能正常启动');
    console.log('=' .repeat(60));

    // 读取路由文件
    let content = this.readRouteFile();
    if (!content) {
      return false;
    }

    // 执行各种修复
    content = this.fixDuplicateCatchBlocks(content);
    content = this.fixUnmatchedBraces(content);
    content = this.cleanupFormatting(content);

    // 验证语法
    const isValid = this.validateSyntax(content);
    if (!isValid) {
      console.log('\n⚠️  语法验证失败，但仍然尝试保存修复后的文件');
    }

    // 写入修复后的文件
    const success = this.writeRouteFile(content);
    
    if (success) {
      console.log('\n🎉 语法错误修复完成！');
      console.log('📋 修复摘要:');
      this.fixes.forEach(fix => {
        console.log(`   ${fix.success ? '✅' : '❌'} ${fix.operation}: ${fix.description}`);
      });
      
      console.log('\n🔄 请重启服务器以验证修复效果...');
      return true;
    } else {
      console.log('\n❌ 修复过程中出现错误');
      return false;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const fixer = new SyntaxFixer();
  fixer.fixAllSyntaxErrors().then(success => {
    console.log(success ? '\n✅ 语法修复完成！' : '\n❌ 修复失败！');
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ 修复过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = SyntaxFixer;
