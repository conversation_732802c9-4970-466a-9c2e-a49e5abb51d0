/**
 * 智慧养鹅SAAS管理后台 - 详细模块和按钮测试
 * 使用Playwright进行深度UI交互测试
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class DetailedModuleButtonTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.context = null;
    this.baseURL = 'http://localhost:4000';
    this.testResults = [];
    this.screenshots = [];
    
    // 测试凭据
    this.credentials = {
      username: 'super_admin',
      password: 'admin123'
    };
    
    // 模块配置
    this.modules = {
      dashboard: {
        name: '平台仪表盘',
        url: '/dashboard',
        buttons: [
          { selector: '.refresh-btn', name: '刷新数据', action: 'click' },
          { selector: '.export-btn', name: '导出报表', action: 'click' },
          { selector: '.date-picker', name: '日期选择器', action: 'click' },
          { selector: '.filter-btn', name: '筛选按钮', action: 'click' }
        ],
        elements: [
          { selector: '.stats-card', name: '统计卡片', check: 'visible' },
          { selector: '.chart-container', name: '图表容器', check: 'visible' },
          { selector: '.data-table', name: '数据表格', check: 'visible' }
        ]
      },
      tenants: {
        name: '租户管理',
        url: '/tenants',
        buttons: [
          { selector: '.add-tenant-btn', name: '添加租户', action: 'click' },
          { selector: '.edit-btn', name: '编辑租户', action: 'click' },
          { selector: '.delete-btn', name: '删除租户', action: 'click' },
          { selector: '.search-btn', name: '搜索按钮', action: 'click' },
          { selector: '.export-btn', name: '导出列表', action: 'click' }
        ],
        elements: [
          { selector: '.tenant-list', name: '租户列表', check: 'visible' },
          { selector: '.search-input', name: '搜索框', check: 'visible' },
          { selector: '.pagination', name: '分页组件', check: 'visible' }
        ]
      },
      goosePrices: {
        name: '今日鹅价管理',
        url: '/goose-prices',
        buttons: [
          { selector: '.add-price-btn', name: '添加价格', action: 'click' },
          { selector: '.edit-price-btn', name: '编辑价格', action: 'click' },
          { selector: '.delete-price-btn', name: '删除价格', action: 'click' },
          { selector: '.publish-btn', name: '发布价格', action: 'click' },
          { selector: '.history-btn', name: '价格历史', action: 'click' }
        ],
        elements: [
          { selector: '.price-list', name: '价格列表', check: 'visible' },
          { selector: '.price-form', name: '价格表单', check: 'exists' },
          { selector: '.price-chart', name: '价格走势图', check: 'exists' }
        ]
      },
      announcements: {
        name: '平台公告管理',
        url: '/announcements',
        buttons: [
          { selector: '.create-announcement-btn', name: '创建公告', action: 'click' },
          { selector: '.edit-announcement-btn', name: '编辑公告', action: 'click' },
          { selector: '.delete-announcement-btn', name: '删除公告', action: 'click' },
          { selector: '.publish-btn', name: '发布公告', action: 'click' },
          { selector: '.preview-btn', name: '预览公告', action: 'click' }
        ],
        elements: [
          { selector: '.announcement-list', name: '公告列表', check: 'visible' },
          { selector: '.editor-container', name: '编辑器容器', check: 'exists' },
          { selector: '.status-filter', name: '状态筛选', check: 'visible' }
        ]
      },
      knowledge: {
        name: '知识库管理',
        url: '/knowledge',
        buttons: [
          { selector: '.add-article-btn', name: '添加文章', action: 'click' },
          { selector: '.edit-article-btn', name: '编辑文章', action: 'click' },
          { selector: '.delete-article-btn', name: '删除文章', action: 'click' },
          { selector: '.category-btn', name: '分类管理', action: 'click' },
          { selector: '.search-btn', name: '搜索文章', action: 'click' }
        ],
        elements: [
          { selector: '.knowledge-tree', name: '知识树', check: 'visible' },
          { selector: '.article-list', name: '文章列表', check: 'visible' },
          { selector: '.category-sidebar', name: '分类侧边栏', check: 'visible' }
        ]
      },
      mall: {
        name: '商城模块管理',
        url: '/mall',
        buttons: [
          { selector: '.add-product-btn', name: '添加商品', action: 'click' },
          { selector: '.edit-product-btn', name: '编辑商品', action: 'click' },
          { selector: '.delete-product-btn', name: '删除商品', action: 'click' },
          { selector: '.category-manage-btn', name: '分类管理', action: 'click' },
          { selector: '.order-manage-btn', name: '订单管理', action: 'click' }
        ],
        elements: [
          { selector: '.product-grid', name: '商品网格', check: 'visible' },
          { selector: '.category-nav', name: '分类导航', check: 'visible' },
          { selector: '.order-summary', name: '订单概览', check: 'exists' }
        ]
      },
      aiConfig: {
        name: 'AI大模型配置',
        url: '/ai-config',
        buttons: [
          { selector: '.save-config-btn', name: '保存配置', action: 'click' },
          { selector: '.test-connection-btn', name: '测试连接', action: 'click' },
          { selector: '.reset-config-btn', name: '重置配置', action: 'click' },
          { selector: '.model-select-btn', name: '模型选择', action: 'click' },
          { selector: '.api-test-btn', name: 'API测试', action: 'click' }
        ],
        elements: [
          { selector: '.config-form', name: '配置表单', check: 'visible' },
          { selector: '.model-list', name: '模型列表', check: 'visible' },
          { selector: '.test-result', name: '测试结果', check: 'exists' }
        ]
      },
      settings: {
        name: '系统设置',
        url: '/settings',
        buttons: [
          { selector: '.save-settings-btn', name: '保存设置', action: 'click' },
          { selector: '.reset-settings-btn', name: '重置设置', action: 'click' },
          { selector: '.backup-btn', name: '备份数据', action: 'click' },
          { selector: '.restore-btn', name: '恢复数据', action: 'click' },
          { selector: '.clear-cache-btn', name: '清除缓存', action: 'click' }
        ],
        elements: [
          { selector: '.settings-tabs', name: '设置标签页', check: 'visible' },
          { selector: '.settings-form', name: '设置表单', check: 'visible' },
          { selector: '.system-info', name: '系统信息', check: 'visible' }
        ]
      }
    };
  }

  // 记录测试结果
  logResult(module, item, type, success, message, details = null) {
    const result = {
      module,
      item,
      type, // 'button', 'element', 'navigation'
      success,
      message,
      details,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} [${module}] ${type}: ${item} - ${message}`);
    if (details) {
      console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
    }
  }

  // 初始化浏览器
  async initBrowser() {
    console.log('🚀 启动Playwright浏览器...');
    try {
      this.browser = await chromium.launch({
        headless: false, // 显示浏览器窗口以便观察
        slowMo: 500,     // 减慢操作速度以便观察
        args: ['--start-maximized']
      });
      
      this.context = await this.browser.newContext({
        viewport: { width: 1920, height: 1080 },
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      });
      
      this.page = await this.context.newPage();
      
      // 设置超时时间
      this.page.setDefaultTimeout(10000);
      this.page.setDefaultNavigationTimeout(15000);
      
      console.log('✅ 浏览器启动成功');
      return true;
    } catch (error) {
      console.error('❌ 浏览器启动失败:', error.message);
      return false;
    }
  }

  // 登录系统
  async login() {
    console.log('\n🔑 执行登录流程...');
    try {
      await this.page.goto(this.baseURL);
      await this.page.waitForLoadState('networkidle');
      
      // 检查是否已经登录
      const currentUrl = this.page.url();
      if (currentUrl.includes('/dashboard')) {
        console.log('✅ 已经登录，跳过登录步骤');
        return true;
      }
      
      // 等待登录页面加载
      await this.page.waitForSelector('input[name="username"], #username', { timeout: 5000 });
      
      // 填写登录信息
      const usernameSelector = await this.page.$('input[name="username"]') ? 'input[name="username"]' : '#username';
      const passwordSelector = await this.page.$('input[name="password"]') ? 'input[name="password"]' : '#password';
      
      await this.page.fill(usernameSelector, this.credentials.username);
      await this.page.fill(passwordSelector, this.credentials.password);
      
      // 点击登录按钮
      const loginButton = await this.page.$('button[type="submit"]') || await this.page.$('.login-btn') || await this.page.$('input[type="submit"]');
      if (loginButton) {
        await loginButton.click();
      } else {
        // 尝试按回车键登录
        await this.page.press(passwordSelector, 'Enter');
      }
      
      // 等待登录完成
      await this.page.waitForURL('**/dashboard', { timeout: 10000 });
      
      console.log('✅ 登录成功');
      return true;
    } catch (error) {
      console.error('❌ 登录失败:', error.message);
      return false;
    }
  }

  // 截图
  async takeScreenshot(name, fullPage = false) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `screenshot-${name}-${timestamp}.png`;
      const filepath = path.join(__dirname, 'screenshots', filename);
      
      // 确保截图目录存在
      const screenshotDir = path.dirname(filepath);
      if (!fs.existsSync(screenshotDir)) {
        fs.mkdirSync(screenshotDir, { recursive: true });
      }
      
      await this.page.screenshot({ 
        path: filepath, 
        fullPage 
      });
      
      this.screenshots.push({
        name,
        filename,
        filepath,
        timestamp
      });
      
      console.log(`📸 截图保存: ${filename}`);
      return filepath;
    } catch (error) {
      console.error('❌ 截图失败:', error.message);
      return null;
    }
  }

  // 测试单个模块
  async testModule(moduleKey, moduleConfig) {
    console.log(`\n🔍 测试模块: ${moduleConfig.name}`);
    console.log('-'.repeat(50));

    try {
      // 导航到模块页面
      await this.page.goto(`${this.baseURL}${moduleConfig.url}`);
      await this.page.waitForLoadState('networkidle');

      // 截图记录页面状态
      await this.takeScreenshot(`${moduleKey}-initial`);

      // 检查页面是否正确加载
      const currentUrl = this.page.url();
      if (currentUrl.includes(moduleConfig.url)) {
        this.logResult(moduleConfig.name, '页面导航', 'navigation', true, '成功导航到模块页面');
      } else {
        this.logResult(moduleConfig.name, '页面导航', 'navigation', false, `导航失败，当前URL: ${currentUrl}`);
        return; // 如果导航失败，跳过该模块的其他测试
      }

      // 等待页面内容加载
      await this.page.waitForTimeout(2000);

      // 测试页面元素
      await this.testModuleElements(moduleKey, moduleConfig);

      // 测试按钮功能
      await this.testModuleButtons(moduleKey, moduleConfig);

    } catch (error) {
      this.logResult(moduleConfig.name, '模块测试', 'module', false, `模块测试失败: ${error.message}`);
      console.error(`❌ 模块 ${moduleConfig.name} 测试失败:`, error.message);
    }
  }

  // 测试模块元素
  async testModuleElements(moduleKey, moduleConfig) {
    console.log(`📋 测试 ${moduleConfig.name} 页面元素...`);

    for (const element of moduleConfig.elements) {
      try {
        const elementHandle = await this.page.$(element.selector);

        if (element.check === 'visible') {
          const isVisible = elementHandle && await elementHandle.isVisible();
          this.logResult(
            moduleConfig.name,
            element.name,
            'element',
            isVisible,
            isVisible ? '元素可见' : '元素不可见或不存在'
          );
        } else if (element.check === 'exists') {
          const exists = elementHandle !== null;
          this.logResult(
            moduleConfig.name,
            element.name,
            'element',
            exists,
            exists ? '元素存在' : '元素不存在'
          );
        }
      } catch (error) {
        this.logResult(
          moduleConfig.name,
          element.name,
          'element',
          false,
          `元素检查失败: ${error.message}`
        );
      }
    }
  }

  // 测试模块按钮
  async testModuleButtons(moduleKey, moduleConfig) {
    console.log(`🔘 测试 ${moduleConfig.name} 按钮功能...`);

    for (const button of moduleConfig.buttons) {
      try {
        // 查找按钮元素
        const buttonElement = await this.page.$(button.selector);

        if (!buttonElement) {
          this.logResult(
            moduleConfig.name,
            button.name,
            'button',
            false,
            '按钮元素不存在'
          );
          continue;
        }

        // 检查按钮是否可见和可点击
        const isVisible = await buttonElement.isVisible();
        const isEnabled = await buttonElement.isEnabled();

        if (!isVisible) {
          this.logResult(
            moduleConfig.name,
            button.name,
            'button',
            false,
            '按钮不可见'
          );
          continue;
        }

        if (!isEnabled) {
          this.logResult(
            moduleConfig.name,
            button.name,
            'button',
            false,
            '按钮被禁用'
          );
          continue;
        }

        // 执行按钮操作
        if (button.action === 'click') {
          await this.testButtonClick(moduleKey, moduleConfig, button, buttonElement);
        }

      } catch (error) {
        this.logResult(
          moduleConfig.name,
          button.name,
          'button',
          false,
          `按钮测试失败: ${error.message}`
        );
      }
    }
  }

  // 测试按钮点击
  async testButtonClick(moduleKey, moduleConfig, button, buttonElement) {
    try {
      // 记录点击前的页面状态
      const beforeUrl = this.page.url();

      // 点击按钮
      await buttonElement.click();

      // 等待可能的页面变化
      await this.page.waitForTimeout(1500);

      // 检查点击后的变化
      const afterUrl = this.page.url();
      const urlChanged = beforeUrl !== afterUrl;

      // 检查是否有模态框或弹窗出现
      const modalExists = await this.page.$('.modal, .dialog, .popup, .overlay') !== null;

      // 检查是否有新的内容加载
      const hasNewContent = await this.checkForNewContent();

      // 截图记录点击后的状态
      await this.takeScreenshot(`${moduleKey}-${button.name.replace(/\s+/g, '-')}-after-click`);

      // 判断按钮点击是否成功
      const clickSuccess = urlChanged || modalExists || hasNewContent;

      this.logResult(
        moduleConfig.name,
        button.name,
        'button',
        clickSuccess,
        clickSuccess ? '按钮点击成功，页面有响应' : '按钮点击无明显响应',
        {
          urlChanged,
          modalExists,
          hasNewContent,
          beforeUrl,
          afterUrl
        }
      );

      // 如果出现了模态框，尝试关闭它
      if (modalExists) {
        await this.closeModalIfExists();
      }

      // 如果URL改变了，导航回原页面
      if (urlChanged && !afterUrl.includes(moduleConfig.url)) {
        await this.page.goto(`${this.baseURL}${moduleConfig.url}`);
        await this.page.waitForLoadState('networkidle');
      }

    } catch (error) {
      this.logResult(
        moduleConfig.name,
        button.name,
        'button',
        false,
        `按钮点击测试失败: ${error.message}`
      );
    }
  }

  // 检查新内容
  async checkForNewContent() {
    try {
      // 检查常见的动态内容指示器
      const indicators = [
        '.loading', '.spinner', '.progress',
        '.success-message', '.error-message', '.alert',
        '.notification', '.toast', '.snackbar',
        '.new-content', '.updated-content'
      ];

      for (const indicator of indicators) {
        const element = await this.page.$(indicator);
        if (element && await element.isVisible()) {
          return true;
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  // 关闭模态框
  async closeModalIfExists() {
    try {
      const closeSelectors = [
        '.modal .close', '.modal .btn-close', '.modal [data-dismiss="modal"]',
        '.dialog .close', '.dialog .btn-close',
        '.popup .close', '.popup .btn-close',
        '.overlay .close', '.overlay .btn-close'
      ];

      for (const selector of closeSelectors) {
        const closeBtn = await this.page.$(selector);
        if (closeBtn && await closeBtn.isVisible()) {
          await closeBtn.click();
          await this.page.waitForTimeout(500);
          return;
        }
      }

      // 尝试按ESC键关闭
      await this.page.keyboard.press('Escape');
      await this.page.waitForTimeout(500);

    } catch (error) {
      console.log('关闭模态框时出错:', error.message);
    }
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始详细模块和按钮测试...');
    console.log('=' .repeat(80));

    try {
      // 初始化浏览器
      const browserReady = await this.initBrowser();
      if (!browserReady) {
        throw new Error('浏览器初始化失败');
      }

      // 登录系统
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('登录失败');
      }

      // 对每个模块进行详细测试
      for (const [moduleKey, moduleConfig] of Object.entries(this.modules)) {
        await this.testModule(moduleKey, moduleConfig);
        await this.page.waitForTimeout(2000); // 模块间等待
      }

      return this.generateDetailedReport();
    } catch (error) {
      console.error('❌ 测试执行过程中发生错误:', error);
      this.logResult('系统', '测试执行', 'system', false, `执行错误: ${error.message}`);
      return this.generateDetailedReport();
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔌 浏览器已关闭');
      }
    }
  }

  // 生成详细测试报告
  generateDetailedReport() {
    console.log('\n📊 生成详细测试报告...');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0;

    // 按模块分组统计
    const moduleStats = {};
    const buttonStats = {};
    const elementStats = {};

    this.testResults.forEach(result => {
      if (!moduleStats[result.module]) {
        moduleStats[result.module] = { total: 0, passed: 0, failed: 0 };
      }
      moduleStats[result.module].total++;
      if (result.success) {
        moduleStats[result.module].passed++;
      } else {
        moduleStats[result.module].failed++;
      }

      if (result.type === 'button') {
        if (!buttonStats[result.module]) {
          buttonStats[result.module] = { total: 0, passed: 0, failed: 0 };
        }
        buttonStats[result.module].total++;
        if (result.success) {
          buttonStats[result.module].passed++;
        } else {
          buttonStats[result.module].failed++;
        }
      }

      if (result.type === 'element') {
        if (!elementStats[result.module]) {
          elementStats[result.module] = { total: 0, passed: 0, failed: 0 };
        }
        elementStats[result.module].total++;
        if (result.success) {
          elementStats[result.module].passed++;
        } else {
          elementStats[result.module].failed++;
        }
      }
    });

    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        successRate: `${successRate}%`,
        testDate: new Date().toISOString(),
        testDuration: 'N/A'
      },
      moduleStats,
      buttonStats,
      elementStats,
      detailedResults: this.testResults,
      screenshots: this.screenshots,
      failedTests: this.testResults.filter(r => !r.success),
      recommendations: this.generateRecommendations()
    };

    // 保存报告到文件
    const reportPath = path.join(__dirname, 'detailed-module-button-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // 生成HTML报告
    const htmlReportPath = this.generateHTMLReport(report);

    console.log(`\n📋 详细测试报告已保存:`);
    console.log(`   JSON报告: ${reportPath}`);
    console.log(`   HTML报告: ${htmlReportPath}`);
    console.log(`\n🎯 测试总结:`);
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过: ${passedTests}`);
    console.log(`   失败: ${failedTests}`);
    console.log(`   成功率: ${successRate}%`);

    // 显示模块统计
    console.log(`\n📊 模块测试统计:`);
    Object.entries(moduleStats).forEach(([module, stats]) => {
      const moduleSuccessRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : 0;
      console.log(`   ${module}: ${stats.passed}/${stats.total} (${moduleSuccessRate}%)`);
    });

    report.reportPath = reportPath;
    report.htmlReportPath = htmlReportPath;
    return report;
  }

  // 生成建议
  generateRecommendations() {
    const recommendations = [];
    const failedResults = this.testResults.filter(r => !r.success);

    // 按问题类型分组
    const issuesByType = {};
    failedResults.forEach(result => {
      const key = `${result.type}-${result.message}`;
      if (!issuesByType[key]) {
        issuesByType[key] = [];
      }
      issuesByType[key].push(result);
    });

    // 生成针对性建议
    Object.entries(issuesByType).forEach(([issueType, results]) => {
      if (results.length > 0) {
        const modules = [...new Set(results.map(r => r.module))];

        if (issueType.includes('元素不存在') || issueType.includes('按钮元素不存在')) {
          recommendations.push({
            type: 'UI元素缺失',
            severity: 'high',
            description: `多个模块中存在UI元素缺失问题`,
            affectedModules: modules,
            suggestion: '检查前端页面模板，确保所有必要的UI元素都已正确渲染'
          });
        }

        if (issueType.includes('500') || issueType.includes('服务器错误')) {
          recommendations.push({
            type: '服务器错误',
            severity: 'critical',
            description: `多个模块返回服务器错误`,
            affectedModules: modules,
            suggestion: '检查后端API实现，修复服务器端逻辑错误'
          });
        }

        if (issueType.includes('按钮点击无明显响应')) {
          recommendations.push({
            type: '交互功能问题',
            severity: 'medium',
            description: `按钮点击后无明显页面响应`,
            affectedModules: modules,
            suggestion: '检查JavaScript事件处理器，确保按钮点击有适当的反馈'
          });
        }
      }
    });

    return recommendations;
  }

  // 生成HTML报告
  generateHTMLReport(report) {
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧养鹅SAAS管理后台 - 详细测试报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-card.success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .stat-card.failed { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); }
        .stat-card h3 { margin: 0 0 10px 0; font-size: 2em; }
        .stat-card p { margin: 0; opacity: 0.9; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .success { color: #27ae60; font-weight: bold; }
        .failed { color: #e74c3c; font-weight: bold; }
        .module-section { margin: 30px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; }
        .recommendation { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .recommendation.critical { background: #f8d7da; border-color: #f5c6cb; }
        .recommendation.high { background: #fff3cd; border-color: #ffeaa7; }
        .recommendation.medium { background: #d1ecf1; border-color: #bee5eb; }
        .screenshot-gallery { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .screenshot-item { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .screenshot-item img { width: 100%; height: 200px; object-fit: cover; }
        .screenshot-item .caption { padding: 10px; background: #f8f9fa; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 智慧养鹅SAAS管理后台 - 详细测试报告</h1>

        <div class="summary">
            <div class="stat-card">
                <h3>${report.summary.totalTests}</h3>
                <p>总测试数</p>
            </div>
            <div class="stat-card success">
                <h3>${report.summary.passedTests}</h3>
                <p>通过测试</p>
            </div>
            <div class="stat-card failed">
                <h3>${report.summary.failedTests}</h3>
                <p>失败测试</p>
            </div>
            <div class="stat-card">
                <h3>${report.summary.successRate}</h3>
                <p>成功率</p>
            </div>
        </div>

        <h2>📊 模块测试统计</h2>
        <table>
            <thead>
                <tr><th>模块</th><th>总测试</th><th>通过</th><th>失败</th><th>成功率</th></tr>
            </thead>
            <tbody>
                ${Object.entries(report.moduleStats).map(([module, stats]) => {
                  const successRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : 0;
                  return `<tr>
                    <td>${module}</td>
                    <td>${stats.total}</td>
                    <td class="success">${stats.passed}</td>
                    <td class="failed">${stats.failed}</td>
                    <td>${successRate}%</td>
                  </tr>`;
                }).join('')}
            </tbody>
        </table>

        <h2>🔘 按钮测试统计</h2>
        <table>
            <thead>
                <tr><th>模块</th><th>按钮总数</th><th>通过</th><th>失败</th><th>成功率</th></tr>
            </thead>
            <tbody>
                ${Object.entries(report.buttonStats).map(([module, stats]) => {
                  const successRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : 0;
                  return `<tr>
                    <td>${module}</td>
                    <td>${stats.total}</td>
                    <td class="success">${stats.passed}</td>
                    <td class="failed">${stats.failed}</td>
                    <td>${successRate}%</td>
                  </tr>`;
                }).join('')}
            </tbody>
        </table>

        <h2>🔧 修复建议</h2>
        ${report.recommendations.map(rec => `
            <div class="recommendation ${rec.severity}">
                <h4>${rec.type} (${rec.severity})</h4>
                <p><strong>问题描述:</strong> ${rec.description}</p>
                <p><strong>影响模块:</strong> ${rec.affectedModules.join(', ')}</p>
                <p><strong>建议:</strong> ${rec.suggestion}</p>
            </div>
        `).join('')}

        <h2>❌ 失败测试详情</h2>
        <table>
            <thead>
                <tr><th>模块</th><th>项目</th><th>类型</th><th>错误信息</th><th>时间</th></tr>
            </thead>
            <tbody>
                ${report.failedTests.map(test => `
                    <tr>
                        <td>${test.module}</td>
                        <td>${test.item}</td>
                        <td>${test.type}</td>
                        <td>${test.message}</td>
                        <td>${new Date(test.timestamp).toLocaleString()}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <p style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            报告生成时间: ${new Date(report.summary.testDate).toLocaleString()}<br>
            测试工具: Playwright + 智慧养鹅测试框架
        </p>
    </div>
</body>
</html>`;

    const htmlReportPath = path.join(__dirname, 'detailed-module-button-test-report.html');
    fs.writeFileSync(htmlReportPath, htmlContent);
    return htmlReportPath;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new DetailedModuleButtonTester();
  tester.runAllTests().then(report => {
    console.log('\n✅ 详细测试完成！');
    console.log(`📊 测试报告已生成: ${report.reportPath}`);
    process.exit(report.summary.failedTests > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}

module.exports = DetailedModuleButtonTester;
