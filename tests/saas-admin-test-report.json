{"summary": {"totalTests": 29, "passedTests": 20, "failedTests": 9, "successRate": "68.97%", "testDate": "2025-08-28T01:58:44.432Z"}, "systemInfo": {"baseURL": "http://localhost:4000", "testEnvironment": "development"}, "results": [{"testName": "系统连通性", "success": true, "message": "系统正常运行，正确重定向到登录页面", "details": null, "timestamp": "2025-08-28T01:58:44.109Z"}, {"testName": "登录页面", "success": true, "message": "登录页面加载成功，包含登录表单", "details": null, "timestamp": "2025-08-28T01:58:44.128Z"}, {"testName": "登录功能", "success": true, "message": "登录成功，用户: super_admin, 端点: /auth/login", "details": null, "timestamp": "2025-08-28T01:58:44.261Z"}, {"testName": "路由保护 /dashboard", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.262Z"}, {"testName": "路由保护 /tenants", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.263Z"}, {"testName": "路由保护 /goose-prices", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.264Z"}, {"testName": "路由保护 /announcements", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.264Z"}, {"testName": "路由保护 /knowledge", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.265Z"}, {"testName": "路由保护 /mall", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.266Z"}, {"testName": "路由保护 /ai-config", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.267Z"}, {"testName": "路由保护 /settings", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.268Z"}, {"testName": "路由保护 /tenant/flocks", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.268Z"}, {"testName": "路由保护 /tenant/inventory", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.269Z"}, {"testName": "路由保护 /tenant/health", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.269Z"}, {"testName": "路由保护 /tenant/finance", "success": true, "message": "正确返回401未授权（需要认证）", "details": null, "timestamp": "2025-08-28T01:58:44.270Z"}, {"testName": "路由保护机制", "success": true, "message": "12个路由受保护，0个路由可直接访问", "details": null, "timestamp": "2025-08-28T01:58:44.270Z"}, {"testName": "已登录路由 /dashboard", "success": true, "message": "路由访问成功", "details": null, "timestamp": "2025-08-28T01:58:44.390Z"}, {"testName": "已登录路由 /tenants", "success": true, "message": "路由访问成功", "details": null, "timestamp": "2025-08-28T01:58:44.400Z"}, {"testName": "已登录路由 /goose-prices", "success": false, "message": "状态码: 500", "details": null, "timestamp": "2025-08-28T01:58:44.402Z"}, {"testName": "已登录路由 /announcements", "success": false, "message": "状态码: 500", "details": null, "timestamp": "2025-08-28T01:58:44.407Z"}, {"testName": "已登录路由 /knowledge", "success": false, "message": "状态码: 500", "details": null, "timestamp": "2025-08-28T01:58:44.409Z"}, {"testName": "已登录路由 /mall", "success": false, "message": "状态码: 500", "details": null, "timestamp": "2025-08-28T01:58:44.411Z"}, {"testName": "已登录路由 /ai-config", "success": false, "message": "状态码: 500", "details": null, "timestamp": "2025-08-28T01:58:44.415Z"}, {"testName": "已登录路由 /settings", "success": true, "message": "路由访问成功", "details": null, "timestamp": "2025-08-28T01:58:44.419Z"}, {"testName": "API端点 /health", "success": false, "message": "系统健康检查 - 端点不存在", "details": null, "timestamp": "2025-08-28T01:58:44.423Z"}, {"testName": "API端点 /api/health", "success": false, "message": "API健康检查 - 端点不存在", "details": null, "timestamp": "2025-08-28T01:58:44.425Z"}, {"testName": "API端点 /api/dashboard/stats", "success": false, "message": "仪表盘统计 - 端点不存在", "details": null, "timestamp": "2025-08-28T01:58:44.428Z"}, {"testName": "API端点 /api/tenants", "success": false, "message": "租户API - 端点不存在", "details": null, "timestamp": "2025-08-28T01:58:44.430Z"}, {"testName": "数据库连接", "success": true, "message": "系统正常运行，数据库连接正常（从启动日志推断）", "details": null, "timestamp": "2025-08-28T01:58:44.431Z"}]}