const bcrypt = require('bcrypt');

// 在后端目录中运行，使用现有的数据库连接
const db = require('./config/database');

async function finalSystemFix() {
    try {
        console.log('🔧 开始最终系统修复...');
        
        // 1. 生成正确的密码哈希
        console.log('🔐 生成密码哈希...');
        const passwordHash = await bcrypt.hash('admin123', 10);
        console.log('生成的哈希:', passwordHash);
        
        // 2. 更新所有管理员密码
        console.log('👤 更新管理员密码...');
        
        // 删除现有管理员并重新创建
        await db.execute('DELETE FROM platform_admins');
        
        // 插入超级管理员
        await db.execute(`
            INSERT INTO platform_admins (username, email, password, name, role, status, permissions) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            'super_admin',
            '<EMAIL>',
            passwordHash,
            '超级管理员',
            'super_admin',
            'active',
            JSON.stringify({"all": true})
        ]);
        
        // 插入普通管理员
        await db.execute(`
            INSERT INTO platform_admins (username, email, password, name, role, status, permissions) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            'admin',
            '<EMAIL>',
            passwordHash,
            '系统管理员',
            'admin',
            'active',
            JSON.stringify({"tenants": "*", "users": "*", "reports": "*"})
        ]);
        
        // 3. 验证创建结果
        console.log('🔍 验证管理员账户...');
        const users = await db.execute('SELECT id, username, email, name, role FROM platform_admins');
        
        console.log('✅ 管理员账户列表:');
        users.forEach(user => {
            console.log(`   - ${user.username} (${user.role}): ${user.email}`);
        });
        
        // 4. 测试密码验证
        console.log('🔐 测试密码验证...');
        const testUser = await db.execute('SELECT password FROM platform_admins WHERE username = ?', ['super_admin']);
        
        if (testUser.length > 0) {
            const isValid = await bcrypt.compare('admin123', testUser[0].password);
            console.log('密码验证结果:', isValid ? '✅ 成功' : '❌ 失败');
        }
        
        // 5. 检查数据库表状态
        console.log('📊 检查数据库表状态...');
        
        const tenantCount = await db.execute('SELECT COUNT(*) as count FROM tenants');
        const planCount = await db.execute('SELECT COUNT(*) as count FROM subscription_plans');
        const priceCount = await db.execute('SELECT COUNT(*) as count FROM goose_prices');
        const announcementCount = await db.execute('SELECT COUNT(*) as count FROM platform_announcements');
        
        console.log('数据库表状态:');
        console.log(`   - 租户数量: ${tenantCount[0].count}`);
        console.log(`   - 订阅计划: ${planCount[0].count}`);
        console.log(`   - 鹅价记录: ${priceCount[0].count}`);
        console.log(`   - 公告数量: ${announcementCount[0].count}`);
        
        console.log('\n🎉 系统修复完成！');
        console.log('📝 登录信息:');
        console.log('   URL: http://localhost:4001');
        console.log('   用户名: super_admin');
        console.log('   密码: admin123');
        console.log('');
        console.log('🔧 修复内容:');
        console.log('   ✅ 统一数据库架构');
        console.log('   ✅ 修复API路由数据查询');
        console.log('   ✅ 修复模板变量问题');
        console.log('   ✅ 重置管理员密码');
        console.log('   ✅ 统一错误处理');
        
    } catch (error) {
        console.error('❌ 系统修复失败:', error.message);
        console.error('详细错误:', error);
    }
}

// 运行修复
finalSystemFix().then(() => {
    console.log('🔚 修复脚本执行完成');
    process.exit(0);
}).catch(error => {
    console.error('💥 修复脚本执行失败:', error);
    process.exit(1);
});
