-- 修复智慧养鹅SAAS管理后台users表结构
USE smart_goose_saas_platform;

-- 检查并添加username字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'username';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN username VARCHAR(50) UNIQUE AFTER id', 
    'SELECT "username column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加password字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'password';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT "" AFTER username', 
    'SELECT "password column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加name字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'name';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN name VARCHAR(100) AFTER password', 
    'SELECT "name column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加email字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'email';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN email VARCHAR(100) UNIQUE AFTER name', 
    'SELECT "email column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加role字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'role';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN role ENUM(''super_admin'', ''platform_admin'', ''tenant_admin'', ''admin'', ''user'') DEFAULT ''user'' AFTER email', 
    'SELECT "role column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加status字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'status';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN status ENUM(''active'', ''inactive'', ''suspended'') DEFAULT ''active'' AFTER role', 
    'SELECT "status column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加created_at字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'created_at';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER status', 
    'SELECT "created_at column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加updated_at字段
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'smart_goose_saas_platform' 
AND table_name = 'users' 
AND column_name = 'updated_at';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at', 
    'SELECT "updated_at column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建默认管理员账号
INSERT IGNORE INTO users (username, password, name, email, role, status, created_at, updated_at) VALUES
('super_admin', SHA2('admin123smartgoose_salt_2024', 256), '超级管理员', '<EMAIL>', 'super_admin', 'active', NOW(), NOW()),
('admin', SHA2('admin123smartgoose_salt_2024', 256), '系统管理员', '<EMAIL>', 'admin', 'active', NOW(), NOW());

-- 显示最终表结构
DESCRIBE users;

-- 显示创建的用户
SELECT id, username, name, email, role, status FROM users;
