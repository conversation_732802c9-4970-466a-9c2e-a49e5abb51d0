{"config": {"configFile": "/Volumes/DATA/千问/智慧养鹅全栈/playwright.config.js", "rootDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-setup.js", "globalTeardown": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 4, "webServer": null}, "suites": [{"title": "e2e/saas-admin-comprehensive-test.spec.js", "file": "e2e/saas-admin-comprehensive-test.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "SAAS后台管理系统全面测试", "file": "e2e/saas-admin-comprehensive-test.spec.js", "line": 17, "column": 6, "specs": [{"title": "2. 仪表板功能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 29482, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('canvas, .chart-container, #chart')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('canvas, .chart-container, #chart')\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('canvas, .chart-container, #chart')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('canvas, .chart-container, #chart')\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/e2e/saas-admin-comprehensive-test.spec.js:55:68", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/e2e/saas-admin-comprehensive-test.spec.js", "column": 68, "line": 55}, "snippet": "\u001b[0m \u001b[90m 53 |\u001b[39m     \n \u001b[90m 54 |\u001b[39m     \u001b[90m// 检查图表存在\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'canvas, .chart-container, #chart'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 56 |\u001b[39m     \n \u001b[90m 57 |\u001b[39m     \u001b[90m// 检查导航菜单\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mconst\u001b[39m navLinks \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.nav-link, .sidebar-item'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/e2e/saas-admin-comprehensive-test.spec.js", "column": 68, "line": 55}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('canvas, .chart-container, #chart')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('canvas, .chart-container, #chart')\u001b[22m\n\n\n\u001b[0m \u001b[90m 53 |\u001b[39m     \n \u001b[90m 54 |\u001b[39m     \u001b[90m// 检查图表存在\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'canvas, .chart-container, #chart'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 56 |\u001b[39m     \n \u001b[90m 57 |\u001b[39m     \u001b[90m// 检查导航菜单\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mconst\u001b[39m navLinks \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.nav-link, .sidebar-item'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Volumes/DATA/千问/智慧养鹅全栈/tests/e2e/saas-admin-comprehensive-test.spec.js:55:68\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-28T05:56:23.947Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/e2e-saas-admin-comprehensive-test-SAAS后台管理系统全面测试-2-仪表板功能测试-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/e2e-saas-admin-comprehensive-test-SAAS后台管理系统全面测试-2-仪表板功能测试-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/e2e-saas-admin-comprehensive-test-SAAS后台管理系统全面测试-2-仪表板功能测试-chromium/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/e2e/saas-admin-comprehensive-test.spec.js", "column": 68, "line": 55}}], "status": "unexpected"}], "id": "f52716f949ba64157692-cc9a970fb642a8d37069", "file": "e2e/saas-admin-comprehensive-test.spec.js", "line": 43, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-28T05:56:23.184Z", "duration": 32530.349000000002, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}