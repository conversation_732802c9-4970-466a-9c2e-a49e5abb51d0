# Page snapshot

```yaml
- generic [ref=e2]:
  - navigation [ref=e3]:
    - list [ref=e4]:
      - listitem [ref=e5]:
        - button "" [ref=e6] [cursor=pointer]:
          - generic [ref=e7] [cursor=pointer]: 
      - listitem [ref=e8]:
        - link " 首页" [ref=e9] [cursor=pointer]:
          - /url: /dashboard
          - generic [ref=e10] [cursor=pointer]: 
          - text: 首页
      - listitem [ref=e11]:
        - link " 租户管理" [ref=e12] [cursor=pointer]:
          - /url: /tenants
          - generic [ref=e13] [cursor=pointer]: 
          - text: 租户管理
      - listitem [ref=e14]:
        - link " 监控" [ref=e15] [cursor=pointer]:
          - /url: /monitoring
          - generic [ref=e16] [cursor=pointer]: 
          - text: 监控
      - listitem [ref=e17]:
        - link " 设置" [ref=e18] [cursor=pointer]:
          - /url: /settings
          - generic [ref=e19] [cursor=pointer]: 
          - text: 设置
    - generic [ref=e21]: 智慧养鹅 SaaS 管理平台
    - list [ref=e22]:
      - listitem [ref=e23]:
        - generic [ref=e26]:
          - searchbox "Search" [ref=e27]
          - button "" [ref=e29] [cursor=pointer]:
            - generic [ref=e30] [cursor=pointer]: 
      - listitem [ref=e31]:
        - button " 3" [ref=e32] [cursor=pointer]:
          - generic [ref=e33] [cursor=pointer]: 
          - generic [ref=e34] [cursor=pointer]: "3"
        - text:  
      - listitem [ref=e35]:
        - button "用户头像 超级管理员" [ref=e36] [cursor=pointer]:
          - img "用户头像" [ref=e37] [cursor=pointer]
          - generic [ref=e38] [cursor=pointer]: 超级管理员
        - text:    
  - complementary [ref=e39]:
    - navigation [ref=e41]:
      - menu [ref=e42]:
        - listitem [ref=e43]:
          - link " 平台仪表盘" [ref=e44] [cursor=pointer]:
            - /url: /dashboard
            - generic [ref=e45] [cursor=pointer]: 
            - paragraph [ref=e46] [cursor=pointer]: 平台仪表盘
        - listitem [ref=e47]: 平台级管理
        - listitem [ref=e48]:
          - link " 租户管理 " [ref=e49] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e50] [cursor=pointer]: 
            - paragraph [ref=e51] [cursor=pointer]:
              - text: 租户管理
              - generic [ref=e52] [cursor=pointer]: 
          - text:  
        - listitem [ref=e53]:
          - link " 今日鹅价" [ref=e54] [cursor=pointer]:
            - /url: /goose-prices
            - generic [ref=e55] [cursor=pointer]: 
            - paragraph [ref=e56] [cursor=pointer]: 今日鹅价
        - listitem [ref=e57]:
          - link " 平台公告" [ref=e58] [cursor=pointer]:
            - /url: /announcements
            - generic [ref=e59] [cursor=pointer]: 
            - paragraph [ref=e60] [cursor=pointer]: 平台公告
        - listitem [ref=e61]:
          - link " 知识库" [ref=e62] [cursor=pointer]:
            - /url: /knowledge
            - generic [ref=e63] [cursor=pointer]: 
            - paragraph [ref=e64] [cursor=pointer]: 知识库
        - listitem [ref=e65]:
          - link " 商城管理" [ref=e66] [cursor=pointer]:
            - /url: /mall
            - generic [ref=e67] [cursor=pointer]: 
            - paragraph [ref=e68] [cursor=pointer]: 商城管理
        - listitem [ref=e69]:
          - link " AI配置" [ref=e70] [cursor=pointer]:
            - /url: /ai-config
            - generic [ref=e71] [cursor=pointer]: 
            - paragraph [ref=e72] [cursor=pointer]: AI配置
        - listitem [ref=e73]: 系统功能
        - listitem [ref=e74]:
          - link " 性能监控" [ref=e75] [cursor=pointer]:
            - /url: /monitoring
            - generic [ref=e76] [cursor=pointer]: 
            - paragraph [ref=e77] [cursor=pointer]: 性能监控
        - listitem [ref=e78]:
          - link " 报表管理" [ref=e79] [cursor=pointer]:
            - /url: /reports
            - generic [ref=e80] [cursor=pointer]: 
            - paragraph [ref=e81] [cursor=pointer]: 报表管理
        - listitem [ref=e82]:
          - link " API文档" [ref=e83] [cursor=pointer]:
            - /url: /api-docs
            - generic [ref=e84] [cursor=pointer]: 
            - paragraph [ref=e85] [cursor=pointer]: API文档
        - listitem [ref=e86]:
          - link " 系统设置" [ref=e87] [cursor=pointer]:
            - /url: /settings
            - generic [ref=e88] [cursor=pointer]: 
            - paragraph [ref=e89] [cursor=pointer]: 系统设置
  - generic [ref=e90]:
    - generic [ref=e93]:
      - heading "系统设置 - 智慧养鹅SAAS管理平台" [level=1] [ref=e95]
      - list [ref=e97]:
        - listitem [ref=e98]:
          - link "首页" [ref=e99] [cursor=pointer]:
            - /url: /dashboard
        - listitem [ref=e100]: / 系统设置 - 智慧养鹅SAAS管理平台
    - generic [ref=e103]:
      - generic [ref=e106]:
        - heading "系统设置" [level=1] [ref=e108]
        - list [ref=e110]:
          - listitem [ref=e111]:
            - link "首页" [ref=e112] [cursor=pointer]:
              - /url: /dashboard
          - listitem [ref=e113]: / 系统设置
      - generic [ref=e115]:
        - generic [ref=e116]:
          - generic [ref=e118]:
            - generic [ref=e119]:
              - heading "150" [level=3] [ref=e120]
              - paragraph [ref=e121]: 总用户数
            - generic [ref=e122]: 
          - generic [ref=e124]:
            - generic [ref=e125]:
              - heading "120" [level=3] [ref=e126]
              - paragraph [ref=e127]: 活跃用户
            - generic [ref=e128]: 
          - generic [ref=e130]:
            - generic [ref=e131]:
              - heading "45%" [level=3] [ref=e132]
              - paragraph [ref=e133]: 磁盘使用率
            - generic [ref=e134]: 
          - generic [ref=e136]:
            - generic [ref=e137]:
              - heading "62%" [level=3] [ref=e138]
              - paragraph [ref=e139]: 内存使用率
            - generic [ref=e140]: 
        - generic [ref=e141]:
          - tablist [ref=e143]:
            - listitem [ref=e144]:
              - tab "基本设置" [ref=e145] [cursor=pointer]
            - listitem [ref=e146]:
              - tab "系统信息" [ref=e147] [cursor=pointer]
            - listitem [ref=e148]:
              - tab "维护工具" [ref=e149] [cursor=pointer]
          - tabpanel [ref=e152]:
            - generic [ref=e153]:
              - generic [ref=e154]:
                - generic [ref=e155]: 站点名称
                - textbox [ref=e157]: 智慧养鹅SAAS管理平台
              - generic [ref=e158]:
                - generic [ref=e159]: 站点URL
                - textbox [ref=e161]: http://localhost:4000
              - generic [ref=e162]:
                - generic [ref=e163]: 管理员邮箱
                - textbox [ref=e165]: <EMAIL>
              - generic [ref=e167]:
                - button "保存设置" [ref=e168] [cursor=pointer]
                - button "应用设置" [active] [ref=e169] [cursor=pointer]
                - button "重置设置" [ref=e170] [cursor=pointer]
  - contentinfo [ref=e171]:
    - strong [ref=e172]: Copyright © 2024 SAAS管理平台.
    - text: 版本 1.0.0
    - generic [ref=e173]:
      - generic [ref=e174]: "技术支持:"
      - text: Smart Goose Team
```