# Page snapshot

```yaml
- generic [ref=e2]:
  - navigation [ref=e3]:
    - list [ref=e4]:
      - listitem [ref=e5]:
        - button "" [ref=e6] [cursor=pointer]:
          - generic [ref=e7] [cursor=pointer]: 
      - listitem [ref=e8]:
        - link " 首页" [ref=e9] [cursor=pointer]:
          - /url: /dashboard
          - generic [ref=e10] [cursor=pointer]: 
          - text: 首页
      - listitem [ref=e11]:
        - link " 租户管理" [ref=e12] [cursor=pointer]:
          - /url: /tenants
          - generic [ref=e13] [cursor=pointer]: 
          - text: 租户管理
      - listitem [ref=e14]:
        - link " 监控" [ref=e15] [cursor=pointer]:
          - /url: /monitoring
          - generic [ref=e16] [cursor=pointer]: 
          - text: 监控
      - listitem [ref=e17]:
        - link " 设置" [ref=e18] [cursor=pointer]:
          - /url: /settings
          - generic [ref=e19] [cursor=pointer]: 
          - text: 设置
    - generic [ref=e21]: 智慧养鹅 SaaS 管理平台
    - list [ref=e22]:
      - listitem [ref=e23]:
        - generic [ref=e26]:
          - searchbox "Search" [active] [ref=e27]
          - button "" [ref=e29] [cursor=pointer]:
            - generic [ref=e30] [cursor=pointer]: 
      - listitem [ref=e31]:
        - button " 3" [ref=e32] [cursor=pointer]:
          - generic [ref=e33] [cursor=pointer]: 
          - generic [ref=e34] [cursor=pointer]: "3"
        - text:  
      - listitem [ref=e35]:
        - button "用户头像 超级管理员" [ref=e36] [cursor=pointer]:
          - img "用户头像" [ref=e37] [cursor=pointer]
          - generic [ref=e38] [cursor=pointer]: 超级管理员
        - text:    
  - complementary [ref=e39]:
    - navigation [ref=e41]:
      - menu [ref=e42]:
        - listitem [ref=e43]:
          - link " 平台仪表盘" [ref=e44] [cursor=pointer]:
            - /url: /dashboard
            - generic [ref=e45] [cursor=pointer]: 
            - paragraph [ref=e46] [cursor=pointer]: 平台仪表盘
        - listitem [ref=e47]: 平台级管理
        - listitem [ref=e48]:
          - link " 租户管理 " [ref=e49] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e50] [cursor=pointer]: 
            - paragraph [ref=e51] [cursor=pointer]:
              - text: 租户管理
              - generic [ref=e52] [cursor=pointer]: 
          - list [ref=e53]:
            - listitem [ref=e54]:
              - link " 租户列表" [ref=e55] [cursor=pointer]:
                - /url: /tenants
                - generic [ref=e56] [cursor=pointer]: 
                - paragraph [ref=e57] [cursor=pointer]: 租户列表
            - listitem [ref=e58]:
              - link " 平台用户" [ref=e59] [cursor=pointer]:
                - /url: /platform-users
                - generic [ref=e60] [cursor=pointer]: 
                - paragraph [ref=e61] [cursor=pointer]: 平台用户
        - listitem [ref=e62]:
          - link " 今日鹅价" [ref=e63] [cursor=pointer]:
            - /url: /goose-prices
            - generic [ref=e64] [cursor=pointer]: 
            - paragraph [ref=e65] [cursor=pointer]: 今日鹅价
        - listitem [ref=e66]:
          - link " 平台公告" [ref=e67] [cursor=pointer]:
            - /url: /announcements
            - generic [ref=e68] [cursor=pointer]: 
            - paragraph [ref=e69] [cursor=pointer]: 平台公告
        - listitem [ref=e70]:
          - link " 知识库" [ref=e71] [cursor=pointer]:
            - /url: /knowledge
            - generic [ref=e72] [cursor=pointer]: 
            - paragraph [ref=e73] [cursor=pointer]: 知识库
        - listitem [ref=e74]:
          - link " 商城管理" [ref=e75] [cursor=pointer]:
            - /url: /mall
            - generic [ref=e76] [cursor=pointer]: 
            - paragraph [ref=e77] [cursor=pointer]: 商城管理
        - listitem [ref=e78]:
          - link " AI配置" [ref=e79] [cursor=pointer]:
            - /url: /ai-config
            - generic [ref=e80] [cursor=pointer]: 
            - paragraph [ref=e81] [cursor=pointer]: AI配置
        - listitem [ref=e82]: 系统功能
        - listitem [ref=e83]:
          - link " 性能监控" [ref=e84] [cursor=pointer]:
            - /url: /monitoring
            - generic [ref=e85] [cursor=pointer]: 
            - paragraph [ref=e86] [cursor=pointer]: 性能监控
        - listitem [ref=e87]:
          - link " 报表管理" [ref=e88] [cursor=pointer]:
            - /url: /reports
            - generic [ref=e89] [cursor=pointer]: 
            - paragraph [ref=e90] [cursor=pointer]: 报表管理
        - listitem [ref=e91]:
          - link " API文档" [ref=e92] [cursor=pointer]:
            - /url: /api-docs
            - generic [ref=e93] [cursor=pointer]: 
            - paragraph [ref=e94] [cursor=pointer]: API文档
        - listitem [ref=e95]:
          - link " 系统设置" [ref=e96] [cursor=pointer]:
            - /url: /settings
            - generic [ref=e97] [cursor=pointer]: 
            - paragraph [ref=e98] [cursor=pointer]: 系统设置
  - generic [ref=e99]:
    - generic [ref=e102]:
      - heading "租户管理" [level=1] [ref=e104]
      - list [ref=e106]:
        - listitem [ref=e107]:
          - link "首页" [ref=e108] [cursor=pointer]:
            - /url: /dashboard
        - listitem [ref=e109]: / 租户管理
    - generic [ref=e113]:
      - generic [ref=e114]:
        - generic [ref=e116]:
          - generic [ref=e117]:
            - heading [level=3]
            - paragraph [ref=e118]: 总租户数
          - generic [ref=e119]: 
        - generic [ref=e121]:
          - generic [ref=e122]:
            - heading "0" [level=3] [ref=e123]
            - paragraph [ref=e124]: 活跃租户
          - generic [ref=e125]: 
        - generic [ref=e127]:
          - generic [ref=e128]:
            - heading "0" [level=3] [ref=e129]
            - paragraph [ref=e130]: 总用户数
          - generic [ref=e131]: 
        - generic [ref=e133]:
          - generic [ref=e134]:
            - heading "¥0" [level=3] [ref=e135]
            - paragraph [ref=e136]: 总收入
          - generic [ref=e137]: $
      - generic [ref=e140]:
        - heading " 租户管理" [level=5] [ref=e141]:
          - generic [ref=e142]: 
          - text: 租户管理
        - generic [ref=e143]:
          - link "+ 创建租户" [ref=e144] [cursor=pointer]:
            - /url: /tenants/create
            - generic [ref=e145] [cursor=pointer]: +
            - text: 创建租户
          - button " 全选" [ref=e146] [cursor=pointer]:
            - generic [ref=e147] [cursor=pointer]: 
            - text: 全选
          - button " 批量暂停" [disabled]:
            - generic: 
            - text: 批量暂停
          - button " 批量激活" [disabled]:
            - generic: 
            - text: 批量激活
          - button " 导出数据" [ref=e148] [cursor=pointer]:
            - generic [ref=e149] [cursor=pointer]: 
            - text: 导出数据
          - link " 订阅管理" [ref=e150] [cursor=pointer]:
            - /url: /tenants/subscriptions
            - generic [ref=e151] [cursor=pointer]: 
            - text: 订阅管理
          - link " 使用统计" [ref=e152] [cursor=pointer]:
            - /url: /tenants/usage
            - generic [ref=e153] [cursor=pointer]: 
            - text: 使用统计
      - text:  
  - contentinfo [ref=e155]:
    - strong [ref=e156]: Copyright © 2024 SAAS管理平台.
    - text: 版本 1.0.0
    - generic [ref=e157]:
      - generic [ref=e158]: "技术支持:"
      - text: Smart Goose Team
```