# Page snapshot

```yaml
- generic [ref=e2]:
  - navigation [ref=e3]:
    - list [ref=e4]:
      - listitem [ref=e5]:
        - button "" [ref=e6] [cursor=pointer]:
          - generic [ref=e7] [cursor=pointer]: 
      - listitem [ref=e8]:
        - link " 首页" [ref=e9] [cursor=pointer]:
          - /url: /dashboard
          - generic [ref=e10] [cursor=pointer]: 
          - text: 首页
      - listitem [ref=e11]:
        - link " 租户管理" [ref=e12] [cursor=pointer]:
          - /url: /tenants
          - generic [ref=e13] [cursor=pointer]: 
          - text: 租户管理
      - listitem [ref=e14]:
        - link " 监控" [ref=e15] [cursor=pointer]:
          - /url: /monitoring
          - generic [ref=e16] [cursor=pointer]: 
          - text: 监控
      - listitem [ref=e17]:
        - link " 设置" [ref=e18] [cursor=pointer]:
          - /url: /settings
          - generic [ref=e19] [cursor=pointer]: 
          - text: 设置
    - generic [ref=e21]: 智慧养鹅 SaaS 管理平台
    - list [ref=e22]:
      - listitem [ref=e23]:
        - generic [ref=e26]:
          - searchbox "Search" [ref=e27]
          - button "" [ref=e29] [cursor=pointer]:
            - generic [ref=e30] [cursor=pointer]: 
      - listitem [ref=e31]:
        - button " 3" [ref=e32] [cursor=pointer]:
          - generic [ref=e33] [cursor=pointer]: 
          - generic [ref=e34] [cursor=pointer]: "3"
        - text:  
      - listitem [ref=e35]:
        - button "用户头像 超级管理员" [ref=e36] [cursor=pointer]:
          - img "用户头像" [ref=e37] [cursor=pointer]
          - generic [ref=e38] [cursor=pointer]: 超级管理员
        - text:    
  - complementary [ref=e39]:
    - navigation [ref=e41]:
      - menu [ref=e42]:
        - listitem [ref=e43]:
          - link " 平台仪表盘" [ref=e44] [cursor=pointer]:
            - /url: /dashboard
            - generic [ref=e45] [cursor=pointer]: 
            - paragraph [ref=e46] [cursor=pointer]: 平台仪表盘
        - listitem [ref=e47]: 平台级管理
        - listitem [ref=e48]:
          - link " 租户管理 " [ref=e49] [cursor=pointer]:
            - /url: "#"
            - generic [ref=e50] [cursor=pointer]: 
            - paragraph [ref=e51] [cursor=pointer]:
              - text: 租户管理
              - generic [ref=e52] [cursor=pointer]: 
          - text:  
        - listitem [ref=e53]:
          - link " 今日鹅价" [ref=e54] [cursor=pointer]:
            - /url: /goose-prices
            - generic [ref=e55] [cursor=pointer]: 
            - paragraph [ref=e56] [cursor=pointer]: 今日鹅价
        - listitem [ref=e57]:
          - link " 平台公告" [ref=e58] [cursor=pointer]:
            - /url: /announcements
            - generic [ref=e59] [cursor=pointer]: 
            - paragraph [ref=e60] [cursor=pointer]: 平台公告
        - listitem [ref=e61]:
          - link " 知识库" [ref=e62] [cursor=pointer]:
            - /url: /knowledge
            - generic [ref=e63] [cursor=pointer]: 
            - paragraph [ref=e64] [cursor=pointer]: 知识库
        - listitem [ref=e65]:
          - link " 商城管理" [ref=e66] [cursor=pointer]:
            - /url: /mall
            - generic [ref=e67] [cursor=pointer]: 
            - paragraph [ref=e68] [cursor=pointer]: 商城管理
        - listitem [ref=e69]:
          - link " AI配置" [ref=e70] [cursor=pointer]:
            - /url: /ai-config
            - generic [ref=e71] [cursor=pointer]: 
            - paragraph [ref=e72] [cursor=pointer]: AI配置
        - listitem [ref=e73]: 系统功能
        - listitem [ref=e74]:
          - link " 性能监控" [ref=e75] [cursor=pointer]:
            - /url: /monitoring
            - generic [ref=e76] [cursor=pointer]: 
            - paragraph [ref=e77] [cursor=pointer]: 性能监控
        - listitem [ref=e78]:
          - link " 报表管理" [ref=e79] [cursor=pointer]:
            - /url: /reports
            - generic [ref=e80] [cursor=pointer]: 
            - paragraph [ref=e81] [cursor=pointer]: 报表管理
        - listitem [ref=e82]:
          - link " API文档" [ref=e83] [cursor=pointer]:
            - /url: /api-docs
            - generic [ref=e84] [cursor=pointer]: 
            - paragraph [ref=e85] [cursor=pointer]: API文档
        - listitem [ref=e86]:
          - link " 系统设置" [ref=e87] [cursor=pointer]:
            - /url: /settings
            - generic [ref=e88] [cursor=pointer]: 
            - paragraph [ref=e89] [cursor=pointer]: 系统设置
  - generic [ref=e90]:
    - generic [ref=e93]:
      - heading "控制台 - 智慧养鹅SAAS管理平台" [level=1] [ref=e95]
      - list [ref=e97]:
        - listitem [ref=e98]:
          - link "首页" [ref=e99] [cursor=pointer]:
            - /url: /dashboard
        - listitem [ref=e100]: / 控制台 - 智慧养鹅SAAS管理平台
    - generic [ref=e102]:
      - generic [ref=e103]:
        - generic [ref=e105]:
          - generic [ref=e106]:
            - heading "4" [level=3] [ref=e107]
            - paragraph [ref=e108]: 总租户数
          - generic [ref=e109]: 
          - link "查看详情 " [ref=e110] [cursor=pointer]:
            - /url: /tenants
            - text: 查看详情
            - generic [ref=e111] [cursor=pointer]: 
        - generic [ref=e113]:
          - generic [ref=e114]:
            - heading "4" [level=3] [ref=e115]
            - paragraph [ref=e116]: 活跃租户
          - generic [ref=e117]: 
          - link "查看详情 " [ref=e118] [cursor=pointer]:
            - /url: /tenants?status=active
            - text: 查看详情
            - generic [ref=e119] [cursor=pointer]: 
        - generic [ref=e121]:
          - generic [ref=e122]:
            - heading "150" [level=3] [ref=e123]
            - paragraph [ref=e124]: 平台用户总数
          - generic [ref=e125]: 
          - link "查看详情 " [ref=e126] [cursor=pointer]:
            - /url: /platform-users
            - text: 查看详情
            - generic [ref=e127] [cursor=pointer]: 
        - generic [ref=e129]:
          - generic [ref=e130]:
            - heading "¥125,600" [level=3] [ref=e131]
            - paragraph [ref=e132]: 本月收入
          - generic [ref=e133]: $
          - link "查看详情 " [ref=e134] [cursor=pointer]:
            - /url: /mall/orders
            - text: 查看详情
            - generic [ref=e135] [cursor=pointer]: 
      - generic [ref=e136]:
        - generic [ref=e138]:
          - generic [ref=e139]:
            - heading "5" [level=3] [ref=e140]
            - paragraph [ref=e141]: 基础版租户
          - generic [ref=e142]: 
        - generic [ref=e144]:
          - generic [ref=e145]:
            - heading "6" [level=3] [ref=e146]
            - paragraph [ref=e147]: 标准版租户
          - generic [ref=e148]: 
        - generic [ref=e150]:
          - generic [ref=e151]:
            - heading "3" [level=3] [ref=e152]
            - paragraph [ref=e153]: 高级版租户
          - generic [ref=e154]: 
        - generic [ref=e156]:
          - generic [ref=e157]:
            - heading "1" [level=3] [ref=e158]
            - paragraph [ref=e159]: 企业版租户
          - generic [ref=e160]: 
      - generic [ref=e161]:
        - generic [ref=e163]:
          - generic [ref=e165]: 
          - generic [ref=e166]:
            - generic [ref=e167]: 鹅群总数
            - generic [ref=e168]: "45"
        - generic [ref=e170]:
          - generic [ref=e172]: 
          - generic [ref=e173]:
            - generic [ref=e174]: 鹅只总数
            - generic [ref=e175]: "1250"
        - generic [ref=e177]:
          - generic [ref=e179]: 
          - generic [ref=e180]:
            - generic [ref=e181]: 今日记录
            - generic [ref=e182]: "28"
        - generic [ref=e184]:
          - generic [ref=e186]: 
          - generic [ref=e187]:
            - generic [ref=e188]: 今日产蛋
            - generic [ref=e189]: "320"
        - generic [ref=e191]:
          - generic [ref=e193]: 
          - generic [ref=e194]:
            - generic [ref=e195]: 商品总数
            - generic [ref=e196]: "85"
        - generic [ref=e198]:
          - generic [ref=e200]: 
          - generic [ref=e201]:
            - generic [ref=e202]: 今日鹅价
            - generic [ref=e203]: "12"
      - generic [ref=e204]:
        - generic [ref=e206]:
          - generic [ref=e207]:
            - heading " 系统状态监控" [level=3] [ref=e208]:
              - generic [ref=e209]: 
              - text: 系统状态监控
            - button "" [ref=e211] [cursor=pointer]:
              - generic [ref=e212] [cursor=pointer]: 
          - generic [ref=e213]:
            - generic [ref=e214]:
              - generic [ref=e216]:
                - generic [ref=e218]: 
                - generic [ref=e219]:
                  - generic: 数据库
                  - generic: 正常
                  - generic: "响应时间: 50ms"
              - generic [ref=e221]:
                - generic [ref=e223]: 
                - generic [ref=e224]:
                  - generic: 即将到期
                  - generic: "0"
                  - generic: 30天内到期订阅
              - generic [ref=e226]:
                - generic [ref=e228]: 
                - generic [ref=e229]:
                  - generic: 待处理订单
                  - generic: "0"
                  - generic: 需要处理的订单
              - generic [ref=e231]:
                - generic [ref=e233]: 
                - generic [ref=e234]:
                  - generic: API端点
                  - generic: 25/30
                  - generic: 活跃端点数
            - generic [ref=e235]:
              - generic [ref=e237]:
                - text: CPU 使用率
                - generic [ref=e238]:
                  - generic [ref=e239]: "25"
                  - text: /100%
              - generic [ref=e243]:
                - text: 内存使用率
                - generic [ref=e244]:
                  - generic [ref=e245]: "45"
                  - text: /100%
              - generic [ref=e249]:
                - text: 磁盘使用率
                - generic [ref=e250]:
                  - generic [ref=e251]: "60"
                  - text: /100%
            - generic [ref=e254]:
              - generic [ref=e256]:
                - generic [ref=e257]: 
                - text: "运行时间: 0小时 9分钟"
              - generic [ref=e259]:
                - generic [ref=e260]: 
                - text: "Node.js: v24.6.0"
        - generic [ref=e262]:
          - heading "用户活跃度" [level=3] [ref=e264]: 用户活跃度
          - generic [ref=e265]:
            - generic [ref=e267]:
              - generic [ref=e268]:
                - text: 周活跃用户
                - generic [ref=e269]:
                  - generic [ref=e270]: "95"
                  - text: /150
              - generic [ref=e273]:
                - text: 月活跃用户
                - generic [ref=e274]:
                  - generic [ref=e275]: "135"
                  - text: /150
              - generic [ref=e278]:
                - text: 活跃用户
                - generic [ref=e279]:
                  - generic [ref=e280]: "120"
                  - text: /150
            - generic [ref=e283]:
              - generic [ref=e284]:
                - generic [ref=e285]: 
                - text: "平台知识库: 43篇文章"
              - generic [ref=e286]:
                - generic [ref=e287]: 
                - text: "活跃公告: 5条"
      - generic [ref=e288]:
        - generic [ref=e290]:
          - generic [ref=e291]:
            - heading " 最新租户" [level=3] [ref=e292]:
              - generic [ref=e293]: 
              - text: 最新租户
            - link "" [ref=e295] [cursor=pointer]:
              - /url: /tenants
              - generic [ref=e296] [cursor=pointer]: 
          - table [ref=e299]:
            - rowgroup [ref=e300]:
              - row "租户名称 联系人 用户数 鹅群数 订阅计划" [ref=e301]:
                - cell "租户名称" [ref=e302]
                - cell "联系人" [ref=e303]
                - cell "用户数" [ref=e304]
                - cell "鹅群数" [ref=e305]
                - cell "订阅计划" [ref=e306]
            - rowgroup [ref=e307]:
              - row "测试养鹅场A TEST001 张三 2 0 高级版" [ref=e308]:
                - cell "测试养鹅场A TEST001" [ref=e309]:
                  - strong [ref=e310]: 测试养鹅场A
                  - generic [ref=e311]: TEST001
                - cell "张三" [ref=e312]
                - cell "2" [ref=e313]:
                  - generic [ref=e314]: "2"
                - cell "0" [ref=e315]:
                  - generic [ref=e316]: "0"
                - cell "高级版" [ref=e317]:
                  - generic [ref=e318]: 高级版
              - row "示例养殖场A DEMO001 张三 1 0 标准版" [ref=e319]:
                - cell "示例养殖场A DEMO001" [ref=e320]:
                  - strong [ref=e321]: 示例养殖场A
                  - generic [ref=e322]: DEMO001
                - cell "张三" [ref=e323]
                - cell "1" [ref=e324]:
                  - generic [ref=e325]: "1"
                - cell "0" [ref=e326]:
                  - generic [ref=e327]: "0"
                - cell "标准版" [ref=e328]:
                  - generic [ref=e329]: 标准版
              - row "示例养殖场B DEMO002 李四 1 0 高级版" [ref=e330]:
                - cell "示例养殖场B DEMO002" [ref=e331]:
                  - strong [ref=e332]: 示例养殖场B
                  - generic [ref=e333]: DEMO002
                - cell "李四" [ref=e334]
                - cell "1" [ref=e335]:
                  - generic [ref=e336]: "1"
                - cell "0" [ref=e337]:
                  - generic [ref=e338]: "0"
                - cell "高级版" [ref=e339]:
                  - generic [ref=e340]: 高级版
              - row "示例养殖场C DEMO003 王五 1 0 企业版" [ref=e341]:
                - cell "示例养殖场C DEMO003" [ref=e342]:
                  - strong [ref=e343]: 示例养殖场C
                  - generic [ref=e344]: DEMO003
                - cell "王五" [ref=e345]
                - cell "1" [ref=e346]:
                  - generic [ref=e347]: "1"
                - cell "0" [ref=e348]:
                  - generic [ref=e349]: "0"
                - cell "企业版" [ref=e350]:
                  - generic [ref=e351]: 企业版
        - generic [ref=e353]:
          - generic [ref=e354]:
            - heading " 最新订单" [level=3] [ref=e355]:
              - generic [ref=e356]: 
              - text: 最新订单
            - link "" [ref=e358] [cursor=pointer]:
              - /url: /mall/orders
              - generic [ref=e359] [cursor=pointer]: 
          - table [ref=e362]:
            - rowgroup [ref=e363]:
              - row "订单号 租户 金额 状态 时间" [ref=e364]:
                - cell "订单号" [ref=e365]
                - cell "租户" [ref=e366]
                - cell "金额" [ref=e367]
                - cell "状态" [ref=e368]
                - cell "时间" [ref=e369]
            - rowgroup [ref=e370]:
              - row "暂无订单数据" [ref=e371]:
                - cell "暂无订单数据" [ref=e372]
  - contentinfo [ref=e373]:
    - strong [ref=e374]: Copyright © 2024 SAAS管理平台.
    - text: 版本 1.0.0
    - generic [ref=e375]:
      - generic [ref=e376]: "技术支持:"
      - text: Smart Goose Team
```